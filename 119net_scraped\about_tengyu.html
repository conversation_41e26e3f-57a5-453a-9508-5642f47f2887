<!DOCTYPE html>
<html lang="zh-CN">
<head>	
	<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
	
		
<title>关于腾御 &#8211; 九消消防物联网</title>
<meta name='robots' content='max-image-preview:large' />



<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; Feed" href="index.html?feed=rss2" />
<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; 评论Feed" href="index.html?feed=comments-rss2" />
		<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.7.2"}};
			!function(e,a,t){var n,r,o,i=a.createElement("canvas"),p=i.getContext&&i.getContext("2d");function s(e,t){var a=String.fromCharCode;p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,e),0,0);e=i.toDataURL();return p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,t),0,0),e===i.toDataURL()}function c(e){var t=a.createElement("script");t.src=e,t.defer=t.type="text/javascript",a.getElementsByTagName("head")[0].appendChild(t)}for(o=Array("flag","emoji"),t.supports={everything:!0,everythingExceptFlag:!0},r=0;r<o.length;r++)t.supports[o[r]]=function(e){if(!p||!p.fillText)return!1;switch(p.textBaseline="top",p.font="600 32px Arial",e){case"flag":return s([127987,65039,8205,9895,65039],[127987,65039,8203,9895,65039])?!1:!s([55356,56826,55356,56819],[55356,56826,8203,55356,56819])&&!s([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]);case"emoji":return!s([55357,56424,8205,55356,57212],[55357,56424,8203,55356,57212])}return!1}(o[r]),t.supports.everything=t.supports.everything&&t.supports[o[r]],"flag"!==o[r]&&(t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&t.supports[o[r]]);t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&!t.supports.flag,t.DOMReady=!1,t.readyCallback=function(){t.DOMReady=!0},t.supports.everything||(n=function(){t.readyCallback()},a.addEventListener?(a.addEventListener("DOMContentLoaded",n,!1),e.addEventListener("load",n,!1)):(e.attachEvent("onload",n),a.attachEvent("onreadystatechange",function(){"complete"===a.readyState&&t.readyCallback()})),(n=t.source||{}).concatemoji?c(n.concatemoji):n.wpemoji&&n.twemoji&&(c(n.twemoji),c(n.wpemoji)))}(window,document,window._wpemojiSettings);
		</script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
	<link rel='stylesheet' id='wp-block-library-css'  href='css/style_1.css' type='text/css' media='all' />

<link rel='stylesheet' id='busiprof-style-css'  href='css/style.css' type='text/css' media='all' />
<link rel='stylesheet' id='bootstrap-css-css'  href='css/bootstrap.css' type='text/css' media='all' />
<link rel='stylesheet' id='busiporf-custom-css-css'  href='css/custom.css' type='text/css' media='all' />
<link rel='stylesheet' id='flexslider-css-css'  href='css/flexslider.css' type='text/css' media='all' />



<link rel='stylesheet' id='font-awesome-css-css'  href='css/font-awesome.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='lazyprof-parent-style-css'  href='css/style.css' type='text/css' media='all' />
<script type='text/javascript' src='js/script_1.js' id='jquery-js'></script>
<script type='text/javascript' src='js/script_2.js' id='bootstrap-js-js'></script>
<script type='text/javascript' src='js/script_3.js' id='busiporf-custom-js-js'></script>
<script type='text/javascript' src='https:' id='xiaofang-js'></script>
<link rel="https://api.w.org/" href="index.htmlindex.php?rest_route=/" /><link rel="alternate" type="application/json" href="index.htmlindex.php?rest_route=/wp/v2/pages/63" />
 
<meta name="generator" content="WordPress 5.7.2" />
<link rel="canonical" href="index.htmlabout_tengyu.html" />
<link rel='shortlink' href='index.html?p=63' />
		<!-- Custom Logo: hide header text -->
		<style id="custom-logo-css" type="text/css">
			.site-title, .site-description {
				position: absolute;
				clip: rect(1px, 1px, 1px, 1px);
			}
		</style>
		<link rel="alternate" type="application/json+oembed" href="index.htmlindex.php?rest_route=%2Foembed%2F1.0%2Fembed&#038;url=https%3A%2F%2Fwww.119.net%2F%3Fpage_id%3D63" />
<link rel="alternate" type="text/xml+oembed" href="index.htmlindex.php?rest_route=%2Foembed%2F1.0%2Fembed&#038;url=https%3A%2F%2Fwww.119.net%2F%3Fpage_id%3D63&#038;format=xml" />
<style type="text/css">.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}</style><link rel="icon" href="images/logo1.png" sizes="32x32" />
<link rel="icon" href="images/logo1.png" sizes="192x192" />
<link rel="apple-touch-icon" href="images/logo1.png" />
<meta name="msapplication-TileImage" content="images/logo1.png" />
	
</head>
<body class="page-template page-template-template-about page-template-template-about-php page page-id-63 wp-custom-logo">
	
<div id="page" class="site">
	<!-- <a class="skip-link busiprof-screen-reader" href="#content">Skip to content</a> -->
<!-- Navbar -->	
<nav class="navbar navbar-default">
	<!-- <div class="container"> -->
	<div class="maincontainer">
		<!-- Brand and toggle get grouped for better mobile display -->
		<div class="navbar-header">
			<span class="navbar-brand"><a href="index.html" class="custom-logo-link" rel="home"><img width="91" height="42" src="images/logo.png" class="custom-logo" alt="九消消防物联网" /></a></span>			<div class="custom-logo-link-url">
	    	<h1 class="site-title"><a class="navbar-brand" href="index.html" >九消消防物联网</a>
	    	</h1>
	    							<p class="site-description">智慧消防 &#8211; 腾御科技</p>
								</div>
				
			<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1">
				<span class="sr-only">Toggle navigation</span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
			</button>
		</div>

		<!-- Collect the nav links, forms, and other content for toggling -->
		<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
			<ul id="menu-menu" class="nav navbar-nav navbar-right"><li id="menu-item-288" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-288"><a href="index.html">首页<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-34" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-34 dropdown"><a>产品与服务<img class="arrow" src="images/brightarrow.png"/><span class="caret"></span></a>
<ul class="dropdown-menu">
	<li id="menu-item-328" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-328"><a href="building_fire_center.html">楼宇消防接入中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-329" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-329"><a href="government_fire_center.html">政府消防数据中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-331" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-331"><a href="group_fire_center.html">集团消防数据中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-330" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-330"><a href="smart_electrical.html">智慧用电<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-332" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-332"><a href="our_services.html">我们的服务<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-738" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-738"><a href="https://book.yunzhan365.com/bookcase/ukwe/index.html">产品资料<img class="arrow" src="images/brightarrow.png"/></a></li>
</ul>
</li>
<li id="menu-item-36" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-36"><a href="success_cases.html">成功案例<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-37" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37"><a href="industry_news.html">行业动态<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-38" class="menu-item menu-item-type-custom menu-item-object-custom current-menu-item menu-item-38 active"><a href="about_tengyu.html">关于腾御<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-39" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-39"><a href="technical_support.html">技术支持<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-40" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-40"><a href="contact_us.html">联系我们<img class="arrow" src="images/brightarrow.png"/></a></li>
</ul>			
		</div>
	</div>
</nav>	
<!-- End of Navbar -->
<!-- <div style="height: 96px;width: 100%;"></div> -->

<style>
	.arrow {display: none;}
	.container {width: 1200px;padding:0;}
	.maincontainer {width:100%;padding:0 100px;}
	.custom-logo {width: 91px;height: 42px;}
	/* #page {position: relative;}
	.navbar {position: fixed;left:0;right:0;z-index: 999999999;} */
	@media screen and (max-width: 1600px) {
		.maincontainer {width:100%;padding:0 24px;}
	}
	@media screen and (max-width: 1200px) {
		.container {width: 100%;}
	}
	@media screen and (max-width: 1100px) {
		.container {width: auto;}
		.maincontainer {width:100%;padding:0 14px 0 24px;}
		.nav > li {z-index: 999;}
		.navbar-nav>li a{display: flex;align-items: center;justify-content: space-between;}
		.navbar-nav li {border-bottom: 1px solid rgba(0,0,0,.05);}
		.navbar-nav li .arrow {width: 16px;height: 16px; display: inline-block}
		.navbar-nav li.dropdown>a .arrow {display: none;}
		.caret {margin-right: 20px;margin-top: 2px;margin-left:0;border-top:6px dashed;border-right: 6px solid transparent;border-left:6px solid transparent;}
		.navbar-nav .dropdown-menu {display: block;background: #F1F1F1;border:0;box-shadow:none;-webkit-box-shadow:none;} 
		.dropdown-menu > li > a {background: #f1f1f1 !important;color: #000;border-bottom:0;padding: 15px;}
		.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {color: #337ab7;}

		.navbar-collapse {position: absolute;right: 0px;left: 0px;background: rgb(255, 255, 255);z-index:9999999;}

		.navbar {min-height: 56px;}
		.navbar-toggle {margin: 8px 15px 8px 0px;}
		.custom-logo {width: 52px;height: 24px;}
		.navbar-default .navbar-toggle {border:0;}

		/* .navbar-toggle {margin: 12px 15px 8px 0px;}
		.logo_imgae {width:96px !important;height: 34px !important;}  */
	}
	
</style>
<section class="aboutus">
    <div class="fan_banner">
        <div class="bannercontent">
            <p class="titletxt">关于腾御</p>
            <p class="p2">提供智慧消防、安全管理、物业管理等服务</p>
        </div>
    </div>
    <div id="main" class="site-main contentbox" style="margin: 0 auto;">
        <div class="nx-column">
            <div class="nx-heading nx-heading-align-left alltitle">
                <div class="nx-heading-inner">
                    <i class="bluebox"></i>
                    <span class="nx-allcaps">懂业务懂互联网</span>
                </div>
            </div>
            <div class="txt">
                <p>腾御(上海)信息科技有限公司，是一家专注于物联网技术应用于消防、安全、节能、管理等领域的高科技企业。公司主要产品主要应用在消防物联网、物业管理、安全管理等方面。</p>
                <p>主要应用场景有：政府、银行、集团、景区、医院、学校、物流园、工厂等。</p>
                <p>为他们提供智慧消防、安全管理、物业管理等服务。公司产品已经应用于绿地集团、百联集团、中石化、同济大学等大量高端客户。</p>
                <p>公司团队中有从事十余年消防工程实施经验的专家;有资深物业管理负责人;还有十几年一直从事互联网产品研发、有过很多成功案例的产品负责人。解决了懂业务不懂互联网，懂互联网不懂业务的难题。
                我们的产品，有思想，扣细节，重执行。能落地、能执行、能解决问题。取得了用户的一致好评。</p>
            </div>
        </div>
        <div class="nx-column leftcolumn nx-column-size-1-2">
            <div class="nx-heading nx-heading-align-left alltitle">
                <div class="nx-heading-inner">
                    <i class="bluebox"></i>
                    <span class="nx-allcaps">我们的主张 <span class="redtxt">新消防 新模式</span></span>
                </div>
            </div>
            <div class="txt">
                <p>以数据驱动管理，将消防运维过程的人、事、物三者以数据化的方式呈现。以各种传感器替代繁重的人工巡查。</p>
                <p>将所有工作流程可视化，在关键节点明确给出操作建议。有效降低工作难度、管理难度，提高故障、隐患的处理速度，降低事故发生的概率。</p>
            </div>
        </div>
        <div class="nx-column rightcolumn nx-column-size-1-2">
            <div class="nx-heading nx-heading-align-left alltitle">
                <div class="nx-heading-inner">
                    <i class="bluebox"></i>
                    <span class="nx-allcaps">我们的<span class="redtxt">使命</span></span>
                </div>
            </div>
            <div class="txt">
                <p>我们要尽最大的努力，实现全平台零安全事故！我们今天做的每一件事情，都是在降低客户灾害发生的机率。当有一天，我们服务于数以万计的客户的时候，我们可以骄傲地说：我们在保护着中国、甚至全球很多的建筑物，我们在为上千万、上亿人提供安全保障！</p>
                <p>这就是我们愿意为之付出心血、汗水，以百分之两百的努力去拼搏、实现的目标！</p>
            </div>
        </div>
        <div class="nx-column leftcolumn nx-column-size-1-2">
            <div class="nx-heading nx-heading-align-left alltitle">
                <div class="nx-heading-inner">
                    <i class="bluebox"></i>
                    <span class="nx-allcaps">我们在<span class="redtxt">行动</span></span>
                </div>
            </div>
            <div class="txt">
                <div class="pitem">
                    <p><i class="redpoint"></i></p>
                    <p>2018年5月第一代智慧消防物联网平台上线运行。</p>
                </div>
                <div class="pitem">
                    <p><i class="redpoint"></i></p>
                    <p>2019年3月第二代智慧消防物联网平台：九消消防接入中心重磅上线！除了更加完善的软件平台以外，还包含新一代自主研发的数据采集硬件：九消消防数据采集终端。</p>
                </div>
                <div class="pitem">
                    <p><i class="redpoint"></i></p>
                    <p>2019年4月九消消防维保平台正式开始研发，计划2019年8月发布！</p>
                </div>
                <div class="pitem">
                    <p><i class="redpoint"></i></p>
                    <p>2019年5月九消消防数据中心上线！提供以城市为单位的集中式管理和数据分析功能，标志着九消消防大平台中最重要的一环被补上。可以支持应急管理部门或者大型集团客户实现更高级别的应用。</p>
                </div>
                <div class="pitem">
                    <p><i class="redpoint"></i></p>
                    <p>2020年，以建筑安全为核心的多维大楼安全管理大平台将正式建立、以消防安全、安全用电为切入点，完善建筑管理，打造智慧楼宇管理平台。利用物联网技术将传统的人为管理、改变为数据化管理，提高效率，降低风险，降低管理成本。</p>
                </div>
            </div>
        </div>
        <div class="nx-column rightcolumn nx-column-size-1-2">
            <div class="nx-heading nx-heading-align-left alltitle">
                <div class="nx-heading-inner">
                    <i class="bluebox"></i>
                    <span class="nx-allcaps"><span class="redtxt">加入</span>我们的团队</span>
                </div>
            </div>
            <div class="txt">
                <p>腾御(上海)是一支由互联网通信、消防硬件开发、消防工程专业人士组成的40人精英团队，其中软件研发团队20多人。 </p>
                <p>顾问团队由20余名消防一级工程师组成的专家团队，均在消防行业积累多年的实战经验；高薪聘请世界500强产品总监，组建UX团队，保证产品的用户体验；</p>
                <p>CTO有十几年的项目架构经验，可以全程把控技术框架。项目经理也是由经过PMP项目管理认证的专业人员担任，确保项目的质量。</p>
                <p>如果有兴趣加入我们，请将简历发送至：<strong><EMAIL></strong>。</p>
            </div>
        </div>
    </div>
    <div class="recruit">
        <div class="toptitle">
            <h3>招贤纳士</h3>
        </div>
        <div class="recruitmain" style="display: flex;flex-wrap: wrap;">
            <div class="nx-column nx-column-size-1-3">
                <div class="nx-post-border">
                    <div class="team-thumbnail">
                        <img class="personicon" src="/wp-content/themes/busiprof/images/person1.png" alt="">
                        <h3>NodeJS软件开发工程师</h3>
                        <h4>岗位职责：</h4>
                        <p class="persondesc">
                            基于NodeJS的快速开发平台的设计与实现。<br>
                            物联网业务平台后端逻辑和接口实现。<br>
                            分布式数据存储，海量数据分析，（日增长千万条数据）<br>
                        </p>
                        <div class="detail"><a href="/?p=335"><button>查看详情</button></a></div>
                    </div>
                </div>
            </div>
            <div class="nx-column nx-column-size-1-3">
                <div class="nx-post-border">
                    <div class="team-thumbnail">
                        <img class="personicon" src="/wp-content/themes/busiprof/images/person2.png" alt="">
                        <h3>web前端开发工程师</h3>
                        <h4>岗位职责：</h4>
                        <p class="persondesc">
                            负责数据中心、管理平台、移动Wap、小程序的前段设计与开发；数据可视化的设计与实现;<br>
                            物联网业务平台后端逻辑和接口实现。<br>
                            参与相关页面的Web&amp;Wap前端架构设计、核心代码的编写;进行详细设计、代码开发, 配合测试, 高质量完成项目；按照项目计划，按时提交高质量代码，完成开发任务；<br>
                        </p>
                        <div class="detail"><a href="/?p=408"><button>查看详情</button></a></div>
                    </div>
                </div>
            </div>
            <div class="nx-column nx-column-size-1-3">
                <div class="nx-post-border">
                    <div class="team-thumbnail">
                        <img class="personicon" src="/wp-content/themes/busiprof/images/person3.png" alt="">
                        <h3>销售经理</h3>
                        <h4>岗位职责：</h4>
                        <p class="persondesc">
                            负责公司产品的销售及推广;<br>
                            完成公司制定的销售指标，并定期与上海各区物业经理沟通；<br>
                            负责辖区市场信息的收集及竞争对手的分析；<br>
                            维护客户关系，与客户形成长期战略合作。<br>
                        </p>
                        <div class="detail"><a href="/?p=412"><button>查看详情</button></a></div>
                    </div>
                </div>
            </div>
            <div class="nx-column nx-column-size-1-3">
                <div class="nx-post-border">
                    <div class="team-thumbnail">
                        <img class="personicon" src="/wp-content/themes/busiprof/images/person6.png" alt="">
                        <h3>消防项目经理</h3>
                        <h4>岗位职责：</h4>
                        <p class="persondesc">
                            按照销售部等提供的客户信息及需求，做好消防物联网项目的售前调研，制定解决方案；<br>
                            负责消防物联网施工项目的成本测算，对项目顺利施工、调试、验收、移交负责；<br>
                            负责施工进度、质量、成本、安全等方面的全面把控；<br>
                            对消防物联网项目进行现场维护服务，做好平台使用培训工作，反馈客户使用情况；<br>
                            协助销售部做好业务拓展保障工作、维护期间维护好客户关系，提升客户满意度；<br>
                            配合研发部门对系统应用平台及硬件产品的测试与反馈； <br>
                            协助公司其他部门完成相关工作。<br>
                        </p>
                        <div class="detail"><a href="/?p=417"><button>查看详情</button></a></div>
                    </div>
                </div>
            </div>
            <div class="nx-column nx-column-size-1-3">
                <div class="nx-post-border">
                    <div class="team-thumbnail">
                        <img class="personicon" src="/wp-content/themes/busiprof/images/person4.png" alt="">
                        <h3>电工</h3>
                        <h4>岗位职责：</h4>
                        <p class="persondesc">
                            负责消防物联网硬件设备的现场安装和调试；<br>
                            负责消防物联网售后维护工作的现场实施；<br>
                            完成项目经理安排的其他相关工作。<br>
                        </p>
                        <div class="detail"><a href="/?p=425"><button>查看详情</button></a></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .aboutus {overflow: hidden;}
    .nx-team .nx-post-border .team-thumbnail {padding: 10px !important;}
    h3 {font-weight: 700;}
    .fan_banner {background: url(images/aboutbg.jpg) no-repeat center;background-size: cover;width: 100%;height: 581px;overflow: hidden;}
    .fan_banner .bannercontent {width: 1200px;margin:0 auto;margin-top: 211px;padding-left: 168px;}
    .fan_banner .titletxt {font-size: 72px;color: #000;letter-spacing: 18px;line-height: 132px;}
    .fan_banner .p2 {font-size: 24px;color: #000;margin-top: 24px;}
    .contentbox {width: 1200px;padding-top: 20px;padding-bottom: 128px;}
    .contentbox .nx-column {padding:0;}
    .contentbox .nx-column .txt p {margin-top: 38px;font-size: 16px;line-height: 24px;color: #000;}
    .contentbox .nx-column .txt .pitem {display:flex;}
    .bluebox {display: inline-block;width:7px;background: #0084C9;height:30px;}
    .redtxt {color: #FF0000;}
    .redpoint {display:inline-block;width: 5px;height:5px;background: #FF0000;border-radius: 50%;margin-right: 10px;margin-left: 5px;vertical-align: middle;}
    .alltitle {font-size: 36px;color: #000;margin-top: 140px;}

    .recruit {background: #F6F6F6;overflow: hidden;padding-bottom: 121px;}
    .recruit .toptitle {text-align: center;}
    .recruit .toptitle h3{font-size: 48px;margin: 80px 0;color: #000001;}
    .recruit .recruitmain {width: 1200px;margin:0 auto;}
    .contentbox .leftcolumn {padding-right: 76px;}
    .contentbox .rightcolumn {padding-left: 76px;}
    .recruit .recruitmain .nx-column {padding: 0;}
    .recruit .recruitmain .nx-column .nx-post-border{background: #fff;margin:13px;padding: 36px;}
    .recruit .recruitmain .nx-column .team-thumbnail .personicon{margin-bottom: 40px;}
    .recruit .recruitmain .nx-column .team-thumbnail h3{height: 90px;font-size: 36px;color: #010000;}
    .recruit .recruitmain .nx-column .team-thumbnail h4{font-size: 24px;color: #000000;font-weight: 700;line-height:53px;}
    .recruit .recruitmain .nx-column .team-thumbnail .persondesc{height: 108px;line-height: 27px;overflow : hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 4;-webkit-box-orient: vertical;font-size:18px;color: #000;}
    .recruit .recruitmain .nx-column .team-thumbnail .detail {margin-top: 46px;}
    .recruit .recruitmain .nx-column .team-thumbnail .detail button {display: inline-block;width: 190px;height: 48px;font-size: 18px;background: #fff;border: 1px solid #009AFF;color: #000;}
    .recruit .recruitmain .nx-column .team-thumbnail .detail button a {color: #000;}
    @media screen and (max-width: 1200px) {
        .contentbox {width: 100%;}
        .recruit .recruitmain {width: 100%;}
        .fan_banner .bannercontent {width: 100%;}
    }
    @media screen and (max-width: 1100px) {
        .fan_banner {width: 100%;height: 502px;overflow: hidden;}
        .fan_banner .bannercontent {padding:0;margin: 198px auto 0;text-align: center;}
        .fan_banner .titletxt {font-size: 50px;letter-spacing: 6px;line-height: 66px;}
        .fan_banner .p2 {font-size: 14px;margin-top: 16px;}
        .alltitle {font-size: 24px;margin-top: 48px;font-weight: 700;}
        .contentbox {width: 100%;padding:0 24px;}
        .contentbox .nx-column .txt p {margin-top: 16px;font-size: 14px;}
        .contentbox .nx-column .txt .pitem {display:flex;}
        .bluebox {display: inline-block;width:6px;height:22px;}
        .redtxt {color: #FF0000;}
        .redpoint {display:inline-block;width: 5px;height:5px;background: #FF0000;border-radius: 50%;margin-right: 10px;margin-left: 5px;vertical-align: middle;}

        .recruit {padding-bottom: 48px;}
        .recruit .toptitle h3{font-size: 32px;margin: 48px 0;color: #000001;}
        .recruit .recruitmain {width: 100%;margin:0 auto;}
        .recruit .recruitmain .nx-column {margin:0 !important;}
        .recruit .recruitmain .nx-column .nx-post-border{background: #fff;margin:12px 24px;padding:24px;}
        .recruit .recruitmain .nx-column .team-thumbnail .personicon{margin-bottom: 24px;}
        .recruit .recruitmain .nx-column .team-thumbnail h3{height: 60px;font-size: 24px;}
        .recruit .recruitmain .nx-column .team-thumbnail h4{font-size: 18px;line-height:40px;}
        .recruit .recruitmain .nx-column .team-thumbnail .persondesc{height: 96px;line-height: 24px;overflow : hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 4;-webkit-box-orient: vertical;font-size:14px;color: #000;}
        .recruit .recruitmain .nx-column .team-thumbnail .detail {margin-top: 24px;}
        .recruit .recruitmain .nx-column .team-thumbnail .detail button {width: 177px;height: 32px;font-size: 16px;}
        .recruit .recruitmain .nx-column .team-thumbnail .detail button a {color: #000;}
    }
    @media screen and (max-width: 788px) {
    .fan_banner {background: url(images/aboutbg.jpg) no-repeat -1145px;}
    }
</style>
    
<footer id="footer">
	<div class="container pcbox">		
        <div class="footercont">
            <p class="first"><a href="about_tengyu.html">关于我们</a><a href="success_cases.html">成功案例</a><a href="contact_us.html">合作咨询</a><a href="industry_news.html">行业动态</a></p>
            <p class="second">腾御（上海）信息科技有限公司 | 
              <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a> | 
              <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a>
            </p>
            <p class="third">Copyright©2019TENGYU.,Ltd | 版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
        </div>
	</div>
	<div class="mobbox">	
        <ul>
            <li>
                <a href="about_tengyu.html">
                    <span>关于我们</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="success_cases.html">
                    <span>成功案例</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="contact_us.html">
                    <span>合作咨询</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="industry_news.html">
                    <span>行业动态</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
        </ul>
        <div class="footerbottom">
            <p class="company">腾御（上海）信息科技有限公司</p>
            <p><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a></p>
            <p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a></p>
            <p>Copyright©2019TENGYU.,Ltd</p>
            <p>版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
        </div>
	</div>
</footer>
</div>
<!-- /End of Footer Section -->

<!--Scroll To Top--> 
<a href="#" class="scrollup"><i class="fa fa-chevron-up"></i></a>
<!--/End of Scroll To Top--> 	
<script type='text/javascript' src='js/script_6.js' id='comment-reply-js'></script>
<script type='text/javascript' src='js/script_6.js' id='wp-embed-js'></script>

<style>
    #footer {text-align: center;background: #1E3854;}
    .footercont p {color: #fff;}
    .footercont .first {margin-top: 42px;font-size: 16px;}
    .footercont .first a {margin:0 20px;color: #F0F0F0;}
    .footercont .second {margin-top: 35px;font-size: 10px;}
    .footercont .second a{color: #fff;}
    .footercont .third {margin-top: 5px;font-size: 10px;margin-bottom: 40px;}
    .pcbox {display: block;}
    .mobbox {display: none;}
    @media screen and (max-width: 1100px) {
        .container {width: 100%;}
    }
    @media screen and (max-width: 768px) {
        .container {width: 100%;}
        .pcbox {display: none;}
        .mobbox {display: block;}
        .mobbox ul li a{height: 48px;line-height: 48px;border-bottom:1px solid rgba(255,255,255,.1);margin:0;padding:0;display: flex;justify-content: space-between;align-items: center;padding: 0 24px;}
        .mobbox ul li a span{color: #fff;font-size: 12px;}
        .mobbox ul li img {width: 16px;height:16px;}
        .mobbox .footerbottom {padding:0 24px 27px;}
        .mobbox .footerbottom p {color: #fff;font-size: 12px;text-align: left;}
        .mobbox .footerbottom p.company {line-height: 40px;}
        .mobbox .footerbottom p a{color: #fff;font-size: 12px;}
    }
</style>

<script>
    jQuery(document).ready(function($) {

        $('.i-scrollto').each(function () {
            _this = $(this);

            _this.click(function(){

                var target_offset = _this.offset();
                var target_top = target_offset.top;

                $('html, body').animate({scrollTop:target_top}, 600, 'easeInSine');
            });
        });
    });
    // 如果文章页面body的高度没有整个网页的固定高度高，就让footer部分固定在底部
    console.log(document.body.offsetHeight)
    console.log(document.documentElement.clientHeight)
    // setTimeout(() => {
        if (document.body.offsetHeight<document.documentElement.clientHeight) {
            var footersidebar = document.querySelector('#footer');
            footersidebar.style.position = 'fixed';
            footersidebar.style.bottom = 0;
            footersidebar.style.left = 0;
            footersidebar.style.right = 0;
        }
    // }, 500);
</script>

<link rel="stylesheet" id="nx-box-shortcodes-css" href="css/box-shortcodes.css" type="text/css" media="all" />

</body>
</html>

