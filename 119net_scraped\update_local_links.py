#!/usr/bin/env python3
import os
import re
from pathlib import Path

def update_html_links(html_file):
    """更新HTML文件中的资源链接为本地路径"""
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 定义需要替换的映射
    replacements = {
        # CSS文件
        r'/wp-content/themes/busiprof/css/bootstrap\.css(\?[^"\']*)?': 'css/bootstrap.css',
        r'/wp-content/themes/busiprof/css/custom\.css(\?[^"\']*)?': 'css/custom.css',
        r'/wp-content/themes/busiprof/css/flexslider\.css(\?[^"\']*)?': 'css/flexslider.css',
        r'/wp-content/themes/busiprof/css/font-awesome/css/font-awesome\.min\.css(\?[^"\']*)?': 'css/font-awesome.min.css',
        r'/wp-content/themes/busiprof/style\.css(\?[^"\']*)?': 'css/style.css',
        r'/wp-content/themes/hatatengyu/static/css/box-shortcodes\.css(\?[^"\']*)?': 'css/box-shortcodes.css',
        r'/wp-includes/css/dist/block-library/style\.min\.css(\?[^"\']*)?': 'css/style_1.css',
        
        # JavaScript文件
        r'/wp-content/themes/busiprof/js/jquery\.1\.9\.1\.min\.js(\?[^"\']*)?': 'js/script_1.js',
        r'/wp-content/themes/busiprof/js/bootstrap\.min\.js(\?[^"\']*)?': 'js/script_2.js',
        r'/wp-content/themes/busiprof/js/custom\.js(\?[^"\']*)?': 'js/script_3.js',
        r'/wp-content/themes/busiprof/static/js/jquery\.1\.8\.3\.min\.js(\?[^"\']*)?': 'js/script_5.js',
        r'/wp-includes/js/comment-reply\.min\.js(\?[^"\']*)?': 'js/script_6.js',
        r'/wp-includes/js/wp-embed\.min\.js(\?[^"\']*)?': 'js/script_6.js',
        
        # 图片文件
        r'/wp-content/themes/busiprof/images/brightarrow\.png': 'images/brightarrow.png',
        r'/wp-content/themes/busiprof/images/rightarrow\.png': 'images/rightarrow.png',
        r'/wp-content/themes/busiprof/images/service3\.jpg': 'images/service3.jpg',
        r'/wp-content/themes/busiprof/images/servicebg2\.jpg': 'images/servicebg2.jpg',
        r'/wp-content/themes/busiprof/images/contactbg\.jpg': 'images/contactbg.jpg',
        r'/wp-content/themes/busiprof/images/newsbg\.jpg': 'images/newsbg.jpg',
        r'/wp-content/themes/busiprof/images/support\.jpg': 'images/support.jpg',
        r'/wp-content/themes/busiprof/images/casebgs\.jpg': 'images/casebgs.jpg',
        r'/wp-content/themes/busiprof/images/ouruser\.jpg': 'images/ouruser.jpg',
        r'/wp-content/themes/busiprof/images/servicebg3\.jpg': 'images/servicebg3.jpg',
        r'/wp-content/themes/busiprof/images/aboutbg\.jpg': 'images/aboutbg.jpg',
        r'/wp-content/themes/busiprof/images/access1\.jpg': 'images/access1.jpg',
        r'/wp-content/themes/busiprof/images/andr\.png': 'images/andr.png',
        r'/wp-content/themes/busiprof/images/ios\.png': 'images/ios.png',
        r'/wp-content/themes/busiprof/images/heqrcode\.png': 'images/heqrcode.png',
        
        # 上传的图片
        r'/wp-content/uploads/2019/09/logo1\.png': 'images/logo1.png',
        r'/wp-content/uploads/2020/08/logo\.png': 'images/logo.png',
        r'/wp-content/uploads/2019/10/accessicon1\.png': 'images/accessicon1.png',
        r'/wp-content/uploads/2019/10/accessicon2\.png': 'images/accessicon2.png',
        r'/wp-content/uploads/2019/10/accessicon3\.png': 'images/accessicon3.png',
        r'/wp-content/uploads/2019/10/accessicon4\.png': 'images/accessicon4.png',
        r'/wp-content/uploads/2019/10/accessicon5\.png': 'images/accessicon5.png',
        r'/wp-content/uploads/2022/09/Huolicheng2-1024x560\.png': 'images/Huolicheng2.png',
        r'/wp-content/uploads/2025/03/红利\.png': 'images/hongli.png',
        r'/wp-content/uploads/2025/05/湖南\.png': 'images/hunan.png',
        r'/wp-content/uploads/2025/05/白皮书-1024x535\.png': 'images/baipishu.png',
        r'/wp-content/uploads/2025/06/制作火灾隐患判定规则图片-1024x768\.png': 'images/huozai_guize.png',
        r'/wp-content/uploads/2025/06/图片压缩成功后文件-1024x576\.png': 'images/yasuo_chenggong.png',
        r'/wp-content/uploads/2025/06/火灾报警控制器-728x1024\.png': 'images/huozai_baojing.png',
        
        # 静态文件
        r'/wp-content/themes/busiprof/static/baidumap\.html': 'static/baidumap.html',
        r'/wp-content/themes/busiprof/static/baidumap_mobile\.html': 'static/baidumap_mobile.html',
        
        # 字体文件
        r'/wp-content/static/font/PingFangMedium\.ttf': 'fonts/PingFangMedium.ttf',
        
        # 删除外部资源引用
        r'//fonts\.googleapis\.com[^"\']*': '',
        r'//s\.w\.org[^"\']*': '',
        r'//xiaofang123\.net[^"\']*': '',
        r'https://xiaofang123\.net[^"\']*': '',
        r'http://cdn\.bootcss\.com[^"\']*': '',
        
        # 删除WordPress特定的资源
        r'/wp-includes/wlwmanifest\.xml': '',
        r'/xmlrpc\.php\?rsd': '',
        
        # 保留外部链接（备案等）
        # r'http://www\.beian\.gov\.cn[^"\']*': 保持不变
        # r'https://beian\.miit\.gov\.cn[^"\']*': 保持不变
        # r'https://book\.yunzhan365\.com[^"\']*': 保持不变
    }
    
    # 执行替换
    changes_made = 0
    for pattern, replacement in replacements.items():
        new_content = re.sub(pattern, replacement, content)
        if new_content != content:
            changes_made += 1
            content = new_content
    
    # 移除空的link和script标签
    content = re.sub(r'<link[^>]*href=["\']["\'][^>]*>', '', content)
    content = re.sub(r'<script[^>]*src=["\']["\'][^>]*></script>', '', content)
    
    if content != original_content:
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✓ 更新了 {html_file} ({changes_made} 个替换)")
        return True
    else:
        print(f"○ {html_file} 无需更新")
        return False

def main():
    """主函数"""
    base_dir = "/Users/<USER>/working/tengyu/119.net/119net_scraped"
    html_files = [f for f in os.listdir(base_dir) if f.endswith('.html')]
    
    updated_count = 0
    total_files = len(html_files)
    
    print("开始更新HTML文件中的资源链接...")
    for html_file in html_files:
        file_path = os.path.join(base_dir, html_file)
        if update_html_links(file_path):
            updated_count += 1
    
    print(f"\n更新完成: {updated_count}/{total_files} 个文件已更新")

if __name__ == "__main__":
    main()