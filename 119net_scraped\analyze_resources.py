#!/usr/bin/env python3
import os
import re
from urllib.parse import urljoin, urlparse
import requests
from pathlib import Path

def analyze_html_resources(html_file):
    """分析HTML文件中的外部资源"""
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到所有资源引用
    patterns = [
        r'href=["\']([^"\']+)["\']',  # CSS, links
        r'src=["\']([^"\']+)["\']',   # JS, images
        r'url\(["\']?([^"\']+)["\']?\)', # CSS background images
    ]
    
    resources = set()
    for pattern in patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            if match.startswith('http') or match.startswith('//'):
                resources.add(match)
            elif match.startswith('/wp-') or match.startswith('/xmlrpc') or 'xiaofang123.net' in match:
                resources.add(match)
    
    return resources

def main():
    base_dir = "/Users/<USER>/working/tengyu/119.net/119net_scraped"
    html_files = [f for f in os.listdir(base_dir) if f.endswith('.html')]
    
    all_resources = set()
    
    print("分析HTML文件中的外部资源...")
    for html_file in html_files:
        file_path = os.path.join(base_dir, html_file)
        resources = analyze_html_resources(file_path)
        print(f"\n{html_file}:")
        for resource in sorted(resources):
            print(f"  - {resource}")
            all_resources.add(resource)
    
    print(f"\n总共发现 {len(all_resources)} 个外部资源:")
    for resource in sorted(all_resources):
        print(f"  - {resource}")

if __name__ == "__main__":
    main()