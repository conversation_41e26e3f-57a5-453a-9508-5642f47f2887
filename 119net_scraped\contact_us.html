<!DOCTYPE html>
<html lang="zh-CN">
<head>	
	<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
	
		
<title>联系我们 &#8211; 九消消防物联网</title>
<meta name='robots' content='max-image-preview:large' />



<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; Feed" href="index.html" />
<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; 评论Feed" href="index.html" />
		<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.7.2"}};
			!function(e,a,t){var n,r,o,i=a.createElement("canvas"),p=i.getContext&&i.getContext("2d");function s(e,t){var a=String.fromCharCode;p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,e),0,0);e=i.toDataURL();return p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,t),0,0),e===i.toDataURL()}function c(e){var t=a.createElement("script");t.src=e,t.defer=t.type="text/javascript",a.getElementsByTagName("head")[0].appendChild(t)}for(o=Array("flag","emoji"),t.supports={everything:!0,everythingExceptFlag:!0},r=0;r<o.length;r++)t.supports[o[r]]=function(e){if(!p||!p.fillText)return!1;switch(p.textBaseline="top",p.font="600 32px Arial",e){case"flag":return s([127987,65039,8205,9895,65039],[127987,65039,8203,9895,65039])?!1:!s([55356,56826,55356,56819],[55356,56826,8203,55356,56819])&&!s([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]);case"emoji":return!s([55357,56424,8205,55356,57212],[55357,56424,8203,55356,57212])}return!1}(o[r]),t.supports.everything=t.supports.everything&&t.supports[o[r]],"flag"!==o[r]&&(t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&t.supports[o[r]]);t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&!t.supports.flag,t.DOMReady=!1,t.readyCallback=function(){t.DOMReady=!0},t.supports.everything||(n=function(){t.readyCallback()},a.addEventListener?(a.addEventListener("DOMContentLoaded",n,!1),e.addEventListener("load",n,!1)):(e.attachEvent("onload",n),a.attachEvent("onreadystatechange",function(){"complete"===a.readyState&&t.readyCallback()})),(n=t.source||{}).concatemoji?c(n.concatemoji):n.wpemoji&&n.twemoji&&(c(n.twemoji),c(n.wpemoji)))}(window,document,window._wpemojiSettings);
		</script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
	<link rel='stylesheet' id='wp-block-library-css'  href='css/style_1.css' type='text/css' media='all' />

<link rel='stylesheet' id='busiprof-style-css'  href='css/style.css' type='text/css' media='all' />
<link rel='stylesheet' id='bootstrap-css-css'  href='css/bootstrap.css' type='text/css' media='all' />
<link rel='stylesheet' id='busiporf-custom-css-css'  href='css/custom.css' type='text/css' media='all' />
<link rel='stylesheet' id='flexslider-css-css'  href='css/flexslider.css' type='text/css' media='all' />



<link rel='stylesheet' id='font-awesome-css-css'  href='css/font-awesome.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='lazyprof-parent-style-css'  href='css/style.css' type='text/css' media='all' />
<script type='text/javascript' src='js/script_1.js' id='jquery-js'></script>
<script type='text/javascript' src='js/script_2.js' id='bootstrap-js-js'></script>
<script type='text/javascript' src='js/script_3.js' id='busiporf-custom-js-js'></script>
<script type='text/javascript' src='https:' id='xiaofang-js'></script>
<link rel="" href="" />
 
<meta name="generator" content="WordPress 5.7.2" />
<link rel="canonical" href="index.htmlcontact_us.html" />
<link rel='shortlink' href='index.html?p=65' />
		<!-- Custom Logo: hide header text -->
		<style id="custom-logo-css" type="text/css">
			.site-title, .site-description {
				position: absolute;
				clip: rect(1px, 1px, 1px, 1px);
			}
		</style>
		

<style type="text/css">.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}</style><link rel="icon" href="images/logo1.png" sizes="32x32" />
<link rel="icon" href="images/logo1.png" sizes="192x192" />
<link rel="apple-touch-icon" href="images/logo1.png" />
<meta name="msapplication-TileImage" content="images/logo1.png" />
	
</head>
<body class="page-template page-template-template-contact page-template-template-contact-php page page-id-65 wp-custom-logo">
	
<div id="page" class="site">
	<!-- <a class="skip-link busiprof-screen-reader" href="#content">Skip to content</a> -->
<!-- Navbar -->	
<nav class="navbar navbar-default">
	<!-- <div class="container"> -->
	<div class="maincontainer">
		<!-- Brand and toggle get grouped for better mobile display -->
		<div class="navbar-header">
			<span class="navbar-brand"><a href="index.html" class="custom-logo-link" rel="home"><img width="91" height="42" src="images/logo.png" class="custom-logo" alt="九消消防物联网" /></a></span>			<div class="custom-logo-link-url">
	    	<h1 class="site-title"><a class="navbar-brand" href="index.html" >九消消防物联网</a>
	    	</h1>
	    							<p class="site-description">智慧消防 &#8211; 腾御科技</p>
								</div>
				
			<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1">
				<span class="sr-only">Toggle navigation</span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
			</button>
		</div>

		<!-- Collect the nav links, forms, and other content for toggling -->
		<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
			<ul id="menu-menu" class="nav navbar-nav navbar-right"><li id="menu-item-288" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-288"><a href="index.html">首页<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-34" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-34 dropdown"><a>产品与服务<img class="arrow" src="images/brightarrow.png"/><span class="caret"></span></a>
<ul class="dropdown-menu">
	<li id="menu-item-328" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-328"><a href="building_fire_center.html">楼宇消防接入中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-329" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-329"><a href="government_fire_center.html">政府消防数据中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-331" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-331"><a href="group_fire_center.html">集团消防数据中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-330" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-330"><a href="smart_electrical.html">智慧用电<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-332" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-332"><a href="our_services.html">我们的服务<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-738" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-738"><a href="https://book.yunzhan365.com/bookcase/ukwe/index.html">产品资料<img class="arrow" src="images/brightarrow.png"/></a></li>
</ul>
</li>
<li id="menu-item-36" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-36"><a href="success_cases.html">成功案例<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-37" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37"><a href="industry_news.html">行业动态<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-38" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-38"><a href="about_tengyu.html">关于腾御<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-39" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-39"><a href="technical_support.html">技术支持<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-40" class="menu-item menu-item-type-custom menu-item-object-custom current-menu-item menu-item-40 active"><a href="contact_us.html">联系我们<img class="arrow" src="images/brightarrow.png"/></a></li>
</ul>			
		</div>
	</div>
</nav>	
<!-- End of Navbar -->
<!-- <div style="height: 96px;width: 100%;"></div> -->

<style>
	.arrow {display: none;}
	.container {width: 1200px;padding:0;}
	.maincontainer {width:100%;padding:0 100px;}
	.custom-logo {width: 91px;height: 42px;}
	/* #page {position: relative;}
	.navbar {position: fixed;left:0;right:0;z-index: 999999999;} */
	@media screen and (max-width: 1600px) {
		.maincontainer {width:100%;padding:0 24px;}
	}
	@media screen and (max-width: 1200px) {
		.container {width: 100%;}
	}
	@media screen and (max-width: 1100px) {
		.container {width: auto;}
		.maincontainer {width:100%;padding:0 14px 0 24px;}
		.nav > li {z-index: 999;}
		.navbar-nav>li a{display: flex;align-items: center;justify-content: space-between;}
		.navbar-nav li {border-bottom: 1px solid rgba(0,0,0,.05);}
		.navbar-nav li .arrow {width: 16px;height: 16px; display: inline-block}
		.navbar-nav li.dropdown>a .arrow {display: none;}
		.caret {margin-right: 20px;margin-top: 2px;margin-left:0;border-top:6px dashed;border-right: 6px solid transparent;border-left:6px solid transparent;}
		.navbar-nav .dropdown-menu {display: block;background: #F1F1F1;border:0;box-shadow:none;-webkit-box-shadow:none;} 
		.dropdown-menu > li > a {background: #f1f1f1 !important;color: #000;border-bottom:0;padding: 15px;}
		.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {color: #337ab7;}

		.navbar-collapse {position: absolute;right: 0px;left: 0px;background: rgb(255, 255, 255);z-index:9999999;}

		.navbar {min-height: 56px;}
		.navbar-toggle {margin: 8px 15px 8px 0px;}
		.custom-logo {width: 52px;height: 24px;}
		.navbar-default .navbar-toggle {border:0;}

		/* .navbar-toggle {margin: 12px 15px 8px 0px;}
		.logo_imgae {width:96px !important;height: 34px !important;}  */
	}
	
</style>
<section>
    <div class="fan_banner">
        <p class="p1">联系我们</p>
        <p class="p2">如果您对九消消防系列产品感兴趣，您有志于从事智慧消防行业</p>
        <p class="p2">您可以通过以下方各种方式联系到我们，我们将在第一时间给您反馈</p>
    </div>
    <div class="mapbox">
        <iframe class="pcmap" width="100%" height="1528px" src="static/baidumap.html"></iframe>
        <iframe class="mobmap" width="100%" height="980px" src="static/baidumap_mobile.html"></iframe>
        <div style="position: absolute;left:0;right:0;top:0;bottom:0;"></div>
        <!-- <div class="contactform">
            <div class="checkCode">
                <div class="wrap">
                    <div class="title">请输入验证码</div>
                    <input id="inp" type="text" placeholder="请输入验证码">
                    <canvas id="canvas" width="120" height="40"></canvas>
                    <a href="#" id="changeImg">看不清，换一张</a>
                    <div class="tips"></div>
                    <div class="confirm" id="confirm_btn">确定</div>
                </div>
            </div>
            [contact-form-7 id="279" title="contactus"]        </div> -->
    </div>
</section>

<style>
    .fan_banner {background: url(images/contactbg.jpg) no-repeat center;background-size: cover;width: 100%;height: 581px;text-align: center;overflow: hidden;}
    .fan_banner .p1 {font-size: 72px;color: #fff;margin-top: 201px;margin-bottom: 40px;letter-spacing: 18px;}
    .fan_banner .p2 {font-size: 24px;color: #fff;line-height: 40px;}
    .mapbox {position: relative;}
    .contactform {position: absolute;bottom:200px;left:0;right:0;width: 826px;height: 600px;margin:0 auto;text-align: center;background: #fff;padding:24px 110px;}
    .mapbox .contactform label{display: flex;border-bottom: 1px solid rgba(0,0,0,.2);line-height: 56px;color: #000;}
    .mapbox .contactform label.textcontent{display: block;text-align: left;border-bottom: 1px solid rgba(0,0,0,.2);height: 150px;}
    .mapbox .contactform label.textcontent textarea{border:0;background: #fff;height: 140px;color: #000;box-shadow:0px 0px 1px #fff inset;}
    .mapbox .contactform label input{border:0;background: #fff;margin-left: 20px;color: #000;box-shadow:0px 0px 1px #fff inset;}
    .mapbox .contactform input[type="submit"] {background: #E72E1D;width: 280px;height: 68px;border-radius: 0;margin-top: 66px;}
    .mapbox .contactform input[type="button"] {background: #E72E1D;width: 280px;height: 68px;border-radius: 0;margin-top: 66px;}
    .mapbox .contactform .checkCode {position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%);z-index: 10; width: 100%;height: 100%;border-radius: 10px;background-color: rgba(0,0,0,0.2);display: none;}
    .mapbox .contactform .checkCode .wrap{position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%);width: 400px;height: 300px;border-radius: 10px;background-color: #fff;}
    .mapbox .contactform .checkCode .wrap .title{font-size: 16px;text-align: left;margin:10px;color:#000;font-weight: 500;}
    .mapbox .contactform .checkCode .wrap >input{margin:10px;width:380px;}
    .mapbox .contactform .checkCode .wrap .tips{margin:10px 10px 50px;width:380px;height:20px;line-height: 20px;color:#E72E1D;opacity: 0;transition: all .5s}
    .mapbox .contactform .checkCode .wrap .confirm{background-color: #CC4429;border:none;color:#fff;width:190px;height:50px;line-height: 50px;text-align: center;border-radius: 4px;margin:auto;margin-top:50px;cursor:pointer;}
    .pcmap{display: block;}
    .mobmap{display: none;}
    @media screen and (max-width: 1100px) {
        .fan_banner {width: 100%;height: 502px;}
        .fan_banner .p1 {font-size: 50px;margin-top: 174px;margin-bottom: 16px;letter-spacing: 6px;}
        .fan_banner .p2 {font-size: 14px;line-height: 24px;width: 246px;margin:0 auto;}
        .mapbox {position: relative;}
        .contactform {position: absolute;bottom:48px;left:0;right:0;width: 328px;height:500px;margin:0 auto;text-align: center;background: #fff;padding:24px;}
        .mapbox .contactform label{display: flex;line-height: 40px;}
        .mapbox .contactform label.textcontent{display: block;text-align: left;border-bottom: 1px solid rgba(0,0,0,.2);height: 150px;}
        .mapbox .contactform label.textcontent textarea{border:0;background: #fff;height: 140px;color: #000;}
        .mapbox .contactform label input{margin-left: 0;color: #000;width: 200px;}
        .mapbox .contactform input[type="submit"] {background: #E72E1D;width: 177px;height: 32px;border-radius: 0;margin-top: 48px;}
        .mapbox .contactform input[type="button"] {background: #E72E1D;width: 177px;height: 32px;border-radius: 0;margin-top: 48px;}
        .mapbox .contactform .checkCode {position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%);z-index: 10; width: 100%;height: 100%;border-radius: 10px;background-color: rgba(0,0,0,0.2);display: none;}
        .mapbox .contactform .checkCode .wrap{position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%);width: 400px;height: 300px;border-radius: 10px;background-color: #fff;}
        .mapbox .contactform .checkCode .wrap .title{font-size: 16px;text-align: left;margin:10px;color:#000;font-weight: 500;}
        .mapbox .contactform .checkCode .wrap >input{margin:10px;width:380px;}
        .mapbox .contactform .checkCode .wrap .tips{margin:10px 10px 50px;width:380px;height:20px;line-height: 20px;color:#E72E1D;opacity: 0;transition: all .5s}
        .mapbox .contactform .checkCode .wrap .confirm{background-color: #CC4429;border:none;color:#fff;width:190px;height:50px;line-height: 50px;text-align: center;border-radius: 4px;margin:auto;margin-top:50px;cursor:pointer;}
        .pcmap{display: none;}
        .mobmap{display: block;}
    }
</style>

<footer id="footer">
	<div class="container pcbox">		
        <div class="footercont">
            <p class="first"><a href="about_tengyu.html">关于我们</a><a href="success_cases.html">成功案例</a><a href="contact_us.html">合作咨询</a><a href="industry_news.html">行业动态</a></p>
            <p class="second">腾御（上海）信息科技有限公司 | 
              <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a> | 
              <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a>
            </p>
            <p class="third">Copyright©2019TENGYU.,Ltd | 版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
        </div>
	</div>
	<div class="mobbox">	
        <ul>
            <li>
                <a href="about_tengyu.html">
                    <span>关于我们</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="success_cases.html">
                    <span>成功案例</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="contact_us.html">
                    <span>合作咨询</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="industry_news.html">
                    <span>行业动态</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
        </ul>
        <div class="footerbottom">
            <p class="company">腾御（上海）信息科技有限公司</p>
            <p><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a></p>
            <p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a></p>
            <p>Copyright©2019TENGYU.,Ltd</p>
            <p>版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
        </div>
	</div>
</footer>
</div>
<!-- /End of Footer Section -->

<!--Scroll To Top--> 
<a href="#" class="scrollup"><i class="fa fa-chevron-up"></i></a>
<!--/End of Scroll To Top--> 	
<script type='text/javascript' src='js/script_6.js' id='comment-reply-js'></script>
<script type='text/javascript' src='js/script_6.js' id='wp-embed-js'></script>

<style>
    #footer {text-align: center;background: #1E3854;}
    .footercont p {color: #fff;}
    .footercont .first {margin-top: 42px;font-size: 16px;}
    .footercont .first a {margin:0 20px;color: #F0F0F0;}
    .footercont .second {margin-top: 35px;font-size: 10px;}
    .footercont .second a{color: #fff;}
    .footercont .third {margin-top: 5px;font-size: 10px;margin-bottom: 40px;}
    .pcbox {display: block;}
    .mobbox {display: none;}
    @media screen and (max-width: 1100px) {
        .container {width: 100%;}
    }
    @media screen and (max-width: 768px) {
        .container {width: 100%;}
        .pcbox {display: none;}
        .mobbox {display: block;}
        .mobbox ul li a{height: 48px;line-height: 48px;border-bottom:1px solid rgba(255,255,255,.1);margin:0;padding:0;display: flex;justify-content: space-between;align-items: center;padding: 0 24px;}
        .mobbox ul li a span{color: #fff;font-size: 12px;}
        .mobbox ul li img {width: 16px;height:16px;}
        .mobbox .footerbottom {padding:0 24px 27px;}
        .mobbox .footerbottom p {color: #fff;font-size: 12px;text-align: left;}
        .mobbox .footerbottom p.company {line-height: 40px;}
        .mobbox .footerbottom p a{color: #fff;font-size: 12px;}
    }
</style>

<script>
    jQuery(document).ready(function($) {

        $('.i-scrollto').each(function () {
            _this = $(this);

            _this.click(function(){

                var target_offset = _this.offset();
                var target_top = target_offset.top;

                $('html, body').animate({scrollTop:target_top}, 600, 'easeInSine');
            });
        });
    });
    // 如果文章页面body的高度没有整个网页的固定高度高，就让footer部分固定在底部
    console.log(document.body.offsetHeight)
    console.log(document.documentElement.clientHeight)
    // setTimeout(() => {
        if (document.body.offsetHeight<document.documentElement.clientHeight) {
            var footersidebar = document.querySelector('#footer');
            footersidebar.style.position = 'fixed';
            footersidebar.style.bottom = 0;
            footersidebar.style.left = 0;
            footersidebar.style.right = 0;
        }
    // }, 500);
</script>

<link rel="stylesheet" id="nx-box-shortcodes-css" href="css/box-shortcodes.css" type="text/css" media="all" />

</body>
</html>
<script>

    let btn = document.getElementsByClassName('wpcf7-submit')[0];
    let input = document.getElementById("inp")
    let tips = document.getElementsByClassName("tips")[0]
    let wrap = document.getElementsByClassName("checkCode")[0]
    let a = document.getElementsByClassName('wpcf7-not-valid-tip')

    if(isIE()) {
        
    } else {
        init()
    }


    function init() {
        btn.type = 'button'
        btn.style.background = '#E72E1D'
        // btn.style.width = '280px'
        // btn.style.height = '68px'
        btn.style.borderRadius = '0'
        btn.style.border = '0'
        btn.style.marginTop = '66px'
        btn.style.color = '#FFFFFF'
        btn.style.fontSize = '14px'
        btn.style.textShadow = '1px 2px 2px rgba(0, 0, 0, 0.1)'
        // btn.onclick = function() {
        //     // alert(111)
        //     // if (btn.type === 'button') {
        //     //     wrap.style.display = 'block'
        //     // }
            
        // }
        wrap.style.display = 'none' //初始化默认不展示验证弹出
    }

    var show_num = [] // 验证码
    draw(show_num)

    document.getElementById("changeImg").onclick = function(e){
        e.preventDefault();
        show_num = []
        draw(show_num);
    }
    document.getElementById("confirm_btn").onclick = function(e){
        let value = input.value
        if (!value) {
            // alert('请输入验证码！')
            tips.style.opacity = 1
            tips.innerText = '请输入验证码！'
            setTimeout(()=>{
                tips.style.opacity = 0
            },2000)
        } else if (!checkCode(input.value)) {
            // alert('请输入正确的验证码！')
            tips.style.opacity = 1
            tips.innerText = '请输入正确的验证码！'
            draw(show_num);
            setTimeout(()=>{
                tips.style.opacity = 0
            },2000)
        } else {
            //验证完成，走正常提交表单逻辑
            btn.type = 'submit'
            btn.click()
            setTimeout(()=>{
                btn.type = 'button'
            },50)
        }
    }
    document.getElementsByClassName('wpcf7-submit')[0].onclick = function() {
        let arr = document.getElementsByClassName('wpcf7-form-control')
        let isSwitch = true
        for (let i = 0;i < arr.length; i++) {
            if (arr[i].ariaRequired && !arr[i].value) {
                isSwitch = false
            }
            if (arr[i].name === 'your-email') {
                var email = arr[i].value;
                var reg = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/
                if(!reg.test(email)){
                    // alert("邮箱格式错误");
                    isSwitch = false
                }
            }
        }
        if (!isSwitch) {
            //未填写完整信息或未通过验证，提交表单进行验证
            btn.type = 'submit'
            btn.click()
            setTimeout(()=>{
                btn.type = 'button'
            },50)
        } else {
            //已通过验证，弹出图片验证再次验证
            if (wrap.style.display === 'none') {
                wrap.style.display = 'block'
                input.value = ''
                draw(show_num);
            } else {
                wrap.style.display = 'none'
            }
        }

    }


    function draw(show_num) {
        var canvas = document.getElementById("canvas");
        var canvas_width=canvas.width;
        var canvas_height=canvas.height;
        var context = canvas.getContext("2d");
        context.clearRect(0,0,canvas_width,canvas_height)
        /**绘制背景色**/
        context.fillStyle = randomColor(20,180); //颜色若太深可能导致看不清
        context.fillRect(0,0,canvas_width,canvas_height);
        // canvas.width = canvas_width;
        // canvas.height = canvas_height;
        var sCode = "a,b,c,d,e,f,g,h,i,j,k,m,n,p,q,r,s,t,u,v,w,x,y,z,A,B,C,E,F,G,H,J,K,L,M,N,P,Q,R,S,T,W,X,Y,Z,1,2,3,4,5,6,7,8,9,0";
        var aCode = sCode.split(",");
        var aLength = aCode.length;//获取到数组的长度
        
        for (var i = 0; i < 4; i++) {  //这里的for循环可以控制验证码位数（如果想显示6位数，4改成6即可）
            var j = Math.floor(Math.random() * aLength);//获取到随机的索引值
            // var deg = Math.random() * 30 * Math.PI / 180;//产生0~30之间的随机弧度
            var deg = Math.random() - 0.5; //产生一个随机弧度
            var txt = aCode[j];//得到随机的一个内容
            show_num[i] = txt.toLowerCase();
            var x = 10 + i * 20;//文字在canvas上的x坐标
            var y = 20 + Math.random() * 8;//文字在canvas上的y坐标
            context.font = "bold 23px 微软雅黑";

            context.translate(x, y);
            context.rotate(deg);

            context.fillStyle = randomColor();
            context.fillText(txt, 0, 0);

            context.rotate(-deg);
            context.translate(-x, -y);
        }
        for (var i = 0; i <= 5; i++) { //验证码上显示线条
            context.strokeStyle = randomColor();
            context.beginPath();
            context.moveTo(Math.random() * canvas_width, Math.random() * canvas_height);
            context.lineTo(Math.random() * canvas_width, Math.random() * canvas_height);
            context.stroke();
        }
        for (var i = 0; i <= 30; i++) { //验证码上显示小点
            context.strokeStyle = randomColor();
            context.beginPath();
            var x = Math.random() * canvas_width;
            var y = Math.random() * canvas_height;
            context.moveTo(x, y);
            context.lineTo(x + 1, y + 1);
            context.stroke();
        }
    }

    //得到随机的颜色值
    function randomColor() {
        var r = Math.floor(Math.random() * 256);
        var g = Math.floor(Math.random() * 256);
        var b = Math.floor(Math.random() * 256);
        return "rgb(" + r + "," + g + "," + b + ")";
    }

    //校验验证码是否正确
    function checkCode(str) {
        let rule = show_num.join('')
        return str.toLowerCase() === rule
    }
    

    function isIE() { //ie?
        if (!!window.ActiveXObject || "ActiveXObject" in window)
        return true;
        else
        return false;
    }
</script>



