#!/usr/bin/env python3
import requests
from bs4 import BeautifulSoup
import os
from urllib.parse import urljoin, urlparse
import time
import re

def download_file(url, save_path, headers):
    """下载文件到指定路径"""
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'wb') as f:
            f.write(response.content)
        return True
    except Exception as e:
        print(f"下载失败 {url}: {e}")
        return False

def get_safe_filename(url):
    """从URL生成安全的文件名"""
    parsed = urlparse(url)
    filename = parsed.path.strip('/')
    if not filename:
        filename = 'index.html'
    
    # 替换不安全的字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 如果没有扩展名，根据URL猜测
    if '.' not in filename:
        if 'css' in url.lower():
            filename += '.css'
        elif any(ext in url.lower() for ext in ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']):
            filename += '.jpg'
        else:
            filename += '.html'
    
    return filename

def scrape_119net():
    """抓取119.net页面内容，包括图片和样式文件"""
    
    url = "https://www.119.net"
    
    # 设置请求头，模拟浏览器访问
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        print(f"正在访问 {url}...")
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 设置正确的编码
        response.encoding = response.apparent_encoding
        
        print("页面抓取成功！")
        print(f"页面大小: {len(response.text)} 字符")
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 创建输出目录
        output_dir = "119net_scraped"
        css_dir = os.path.join(output_dir, "css")
        img_dir = os.path.join(output_dir, "images")
        js_dir = os.path.join(output_dir, "js")
        
        for dir_path in [output_dir, css_dir, img_dir, js_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # 下载CSS文件
        print("\n正在下载CSS文件...")
        css_links = soup.find_all('link', rel='stylesheet')
        for i, link in enumerate(css_links):
            css_url = link.get('href')
            if css_url:
                full_css_url = urljoin(url, css_url)
                css_filename = f"style_{i+1}.css"
                css_path = os.path.join(css_dir, css_filename)
                
                if download_file(full_css_url, css_path, headers):
                    print(f"  已下载: {css_filename}")
                    # 更新HTML中的链接
                    link['href'] = f"css/{css_filename}"
                else:
                    print(f"  下载失败: {css_url}")
        
        # 下载图片文件
        print("\n正在下载图片文件...")
        img_tags = soup.find_all('img')
        for i, img in enumerate(img_tags):
            img_url = img.get('src')
            if img_url:
                full_img_url = urljoin(url, img_url)
                # 从URL获取文件扩展名
                parsed_url = urlparse(full_img_url)
                ext = os.path.splitext(parsed_url.path)[1] or '.jpg'
                img_filename = f"image_{i+1}{ext}"
                img_path = os.path.join(img_dir, img_filename)
                
                if download_file(full_img_url, img_path, headers):
                    print(f"  已下载: {img_filename}")
                    # 更新HTML中的链接
                    img['src'] = f"images/{img_filename}"
                else:
                    print(f"  下载失败: {img_url}")
        
        # 下载JavaScript文件
        print("\n正在下载JavaScript文件...")
        js_scripts = soup.find_all('script', src=True)
        for i, script in enumerate(js_scripts):
            js_url = script.get('src')
            if js_url and not js_url.startswith('data:'):
                full_js_url = urljoin(url, js_url)
                js_filename = f"script_{i+1}.js"
                js_path = os.path.join(js_dir, js_filename)
                
                if download_file(full_js_url, js_path, headers):
                    print(f"  已下载: {js_filename}")
                    # 更新HTML中的链接
                    script['src'] = f"js/{js_filename}"
                else:
                    print(f"  下载失败: {js_url}")
        
        # 处理背景图片等CSS中的图片
        print("\n处理内联样式中的图片...")
        for element in soup.find_all(style=True):
            style = element.get('style', '')
            # 查找background-image等CSS属性中的URL
            urls = re.findall(r'url\(["\']?([^"\']*)["\']?\)', style)
            for img_url in urls:
                if img_url:
                    full_img_url = urljoin(url, img_url)
                    parsed_url = urlparse(full_img_url)
                    ext = os.path.splitext(parsed_url.path)[1] or '.jpg'
                    bg_img_filename = f"bg_image_{hash(img_url) % 1000}{ext}"
                    bg_img_path = os.path.join(img_dir, bg_img_filename)
                    
                    if download_file(full_img_url, bg_img_path, headers):
                        print(f"  已下载背景图: {bg_img_filename}")
                        # 更新样式中的URL
                        new_style = style.replace(img_url, f"images/{bg_img_filename}")
                        element['style'] = new_style
        
        # 保存更新后的HTML
        html_filename = os.path.join(output_dir, "index.html")
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        print(f"\n更新后的HTML文件已保存到: {html_filename}")
        
        # 保存原始HTML（用于对比）
        original_html_filename = os.path.join(output_dir, "original.html")
        with open(original_html_filename, 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        print(f"原始HTML文件已保存到: {original_html_filename}")
        
        # 提取并保存页面信息
        info_filename = os.path.join(output_dir, "page_info.txt")
        with open(info_filename, 'w', encoding='utf-8') as f:
            f.write(f"页面URL: {url}\n")
            f.write(f"抓取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"页面标题: {soup.title.string if soup.title else '无标题'}\n")
            f.write(f"页面大小: {len(response.text)} 字符\n")
            f.write(f"状态码: {response.status_code}\n")
            f.write(f"CSS文件数量: {len(css_links)}\n")
            f.write(f"图片数量: {len(img_tags)}\n")
            f.write(f"JS文件数量: {len(js_scripts)}\n")
            
            # 提取meta信息
            f.write("\n--- Meta信息 ---\n")
            for meta in soup.find_all('meta'):
                if meta.get('name') or meta.get('property'):
                    name = meta.get('name') or meta.get('property')
                    content = meta.get('content', '')
                    f.write(f"{name}: {content}\n")
        
        print(f"页面信息已保存到: {info_filename}")
        print("\n抓取完成！")
        print(f"所有文件保存在目录: {output_dir}")
        print("现在可以直接打开 index.html 查看完整页面效果")
        
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    scrape_119net()