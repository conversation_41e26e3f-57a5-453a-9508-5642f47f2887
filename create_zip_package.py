#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建119.net项目的zip包，排除Python脚本文件
"""

import os
import zipfile
import datetime

def should_exclude_file(file_path):
    """判断文件是否应该被排除"""
    # 排除的文件扩展名
    excluded_extensions = ['.py', '.pyc', '.pyo', '.pyd']

    # 排除的目录
    excluded_dirs = ['__pycache__', '.git', '.vscode', 'venv', '.env', 'node_modules']

    # 排除的文件名
    excluded_files = ['.gitignore', '.env', 'requirements.txt', 'Pipfile', 'Pipfile.lock']

    # 检查文件扩展名
    for ext in excluded_extensions:
        if file_path.lower().endswith(ext):
            return True

    # 检查文件名
    filename = os.path.basename(file_path)
    if filename in excluded_files:
        return True

    # 检查路径中是否包含排除的目录
    for excluded_dir in excluded_dirs:
        if excluded_dir in file_path:
            return True

    return False

def create_zip_package():
    """创建zip包"""
    # 获取当前时间作为版本号
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = "119net_website_{}.zip".format(timestamp)

    print("开始创建119.net网站包: {}".format(zip_filename))
    print("=" * 50)

    # 统计信息
    total_files = 0
    included_files = 0
    excluded_files = 0

    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED, allowZip64=True) as zipf:
        # 只包含119net_scraped目录和README.md
        target_dirs = ['119net_scraped']
        target_files = ['README.md']

        # 添加单独的文件
        for target_file in target_files:
            if os.path.exists(target_file):
                total_files += 1
                if not should_exclude_file(target_file):
                    zipf.write(target_file, target_file)
                    included_files += 1
                    print("包含: {}".format(target_file))
                else:
                    excluded_files += 1
                    print("排除: {}".format(target_file))

        # 遍历目标目录
        for target_dir in target_dirs:
            if os.path.exists(target_dir):
                for root, dirs, files in os.walk(target_dir):
                    # 过滤掉排除的目录
                    dirs[:] = [d for d in dirs if not should_exclude_file(os.path.join(root, d))]

                    for file in files:
                        file_path = os.path.join(root, file)
                        total_files += 1

                        if should_exclude_file(file_path):
                            excluded_files += 1
                            print("排除: {}".format(file_path))
                        else:
                            # 计算在zip中的路径
                            arcname = file_path.replace('\\', '/')
                            zipf.write(file_path, arcname)
                            included_files += 1
                            print("包含: {}".format(file_path))

    print("=" * 50)
    print("统计信息:")
    print("   总文件数: {}".format(total_files))
    print("   包含文件: {}".format(included_files))
    print("   排除文件: {}".format(excluded_files))
    print("   压缩包大小: {:.2f} MB".format(os.path.getsize(zip_filename) / 1024 / 1024))
    print("成功创建: {}".format(zip_filename))

    # 创建说明文件
    readme_content = """# 119.net 网站包

## 包信息
- 创建时间: {}
- 包含文件: {} 个
- 版本: {}

## 目录结构
- 119net_scraped/ - 网站主目录
  - index.html - 首页
  - css/ - 样式文件
  - js/ - JavaScript文件
  - images/ - 图片资源
  - fonts/ - 字体文件
  - data/ - 数据文件
  - *.html - 各个页面文件

## 部署说明
1. 解压到Web服务器目录
2. 确保服务器支持静态文件服务
3. 访问 index.html 即可

## 技术特性
- 响应式设计
- 动态统计数据（调用生产API）
- 完整的页面导航
- 优化的资源加载

## 联系信息
如有问题，请联系技术支持。

---
生成时间: {}
""".format(
        datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S"),
        included_files,
        timestamp,
        datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    )

    readme_filename = "119net_website_{}_README.txt".format(timestamp)
    with open(readme_filename, 'w', encoding='utf-8') as f:
        f.write(readme_content)

    print("已创建说明文件: {}".format(readme_filename))
    print("打包完成！")

if __name__ == "__main__":
    create_zip_package()
