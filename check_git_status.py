#!/usr/bin/env python3
"""
Git状态检查脚本
帮助诊断为什么文件修改没有出现在Git记录中
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return -1, "", str(e)

def check_git_repository():
    """检查是否在Git仓库中"""
    print("🔍 检查Git仓库状态...")
    
    # 检查是否是Git仓库
    returncode, stdout, stderr = run_command("git rev-parse --is-inside-work-tree")
    if returncode != 0:
        print("❌ 当前目录不是Git仓库")
        print(f"错误: {stderr}")
        return False
    
    print("✅ 当前目录是Git仓库")
    
    # 获取仓库根目录
    returncode, repo_root, stderr = run_command("git rev-parse --show-toplevel")
    if returncode == 0:
        print(f"📁 仓库根目录: {repo_root}")
    
    return True

def check_git_status():
    """检查Git状态"""
    print("\n📊 检查Git状态...")
    
    # 检查工作区状态
    returncode, stdout, stderr = run_command("git status --porcelain")
    if returncode != 0:
        print(f"❌ 无法获取Git状态: {stderr}")
        return
    
    if not stdout:
        print("✅ 工作区干净，没有未提交的更改")
    else:
        print("📝 发现未提交的更改:")
        for line in stdout.split('\n'):
            if line.strip():
                status = line[:2]
                filename = line[3:]
                status_desc = {
                    'M ': '已修改(已暂存)',
                    ' M': '已修改(未暂存)',
                    'A ': '新文件(已暂存)',
                    '??': '未跟踪文件',
                    'D ': '已删除(已暂存)',
                    ' D': '已删除(未暂存)',
                    'R ': '已重命名',
                    'C ': '已复制',
                    'U ': '未合并'
                }.get(status, f'未知状态({status})')
                print(f"  {status_desc}: {filename}")

def check_gitignore():
    """检查.gitignore文件"""
    print("\n🚫 检查.gitignore文件...")
    
    gitignore_files = ['.gitignore', '.git/info/exclude']
    
    for gitignore_path in gitignore_files:
        if os.path.exists(gitignore_path):
            print(f"📄 找到 {gitignore_path}:")
            try:
                with open(gitignore_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        print("内容:")
                        for i, line in enumerate(content.split('\n'), 1):
                            print(f"  {i:2d}: {line}")
                    else:
                        print("  (文件为空)")
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
        else:
            print(f"❌ 未找到 {gitignore_path}")

def check_specific_file(filepath):
    """检查特定文件的Git状态"""
    print(f"\n🔍 检查文件: {filepath}")
    
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return
    
    # 检查文件是否被Git跟踪
    returncode, stdout, stderr = run_command(f"git ls-files --error-unmatch {filepath}")
    if returncode != 0:
        print(f"❌ 文件未被Git跟踪: {filepath}")
        print("可能原因:")
        print("  1. 文件在.gitignore中被忽略")
        print("  2. 文件从未被添加到Git")
        print(f"解决方法: git add {filepath}")
    else:
        print(f"✅ 文件已被Git跟踪: {filepath}")
    
    # 检查文件状态
    returncode, stdout, stderr = run_command(f"git status --porcelain {filepath}")
    if returncode == 0:
        if stdout.strip():
            status = stdout[:2]
            status_desc = {
                'M ': '已修改(已暂存)',
                ' M': '已修改(未暂存)',
                'A ': '新文件(已暂存)',
                '??': '未跟踪文件',
                'D ': '已删除(已暂存)',
                ' D': '已删除(未暂存)',
            }.get(status, f'状态: {status}')
            print(f"📝 文件状态: {status_desc}")
        else:
            print("✅ 文件没有更改")
    
    # 检查文件是否被忽略
    returncode, stdout, stderr = run_command(f"git check-ignore {filepath}")
    if returncode == 0:
        print(f"🚫 文件被.gitignore忽略")
        print(f"忽略规则: {stdout}")
    else:
        print("✅ 文件未被.gitignore忽略")

def check_git_config():
    """检查Git配置"""
    print("\n⚙️ 检查Git配置...")
    
    configs = [
        ("user.name", "用户名"),
        ("user.email", "邮箱"),
        ("core.autocrlf", "换行符处理"),
        ("core.filemode", "文件权限"),
    ]
    
    for config_key, desc in configs:
        returncode, stdout, stderr = run_command(f"git config {config_key}")
        if returncode == 0 and stdout:
            print(f"✅ {desc}: {stdout}")
        else:
            print(f"❌ {desc}: 未设置")

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 解决方案建议:")
    print("1. 如果文件未被跟踪:")
    print("   git add 119net_scraped/index.html")
    print("   git commit -m '添加index.html文件'")
    print()
    print("2. 如果文件被.gitignore忽略:")
    print("   编辑.gitignore文件，移除相关忽略规则")
    print("   或使用: git add -f 119net_scraped/index.html (强制添加)")
    print()
    print("3. 如果文件已修改但未显示:")
    print("   git add 119net_scraped/index.html")
    print("   git status")
    print()
    print("4. 查看文件修改历史:")
    print("   git log --follow 119net_scraped/index.html")
    print()
    print("5. 查看文件差异:")
    print("   git diff 119net_scraped/index.html")

def main():
    """主函数"""
    print("🔥 Git状态检查工具")
    print("=" * 50)
    
    # 检查Git仓库
    if not check_git_repository():
        print("\n❌ 请在Git仓库中运行此脚本")
        return
    
    # 检查Git状态
    check_git_status()
    
    # 检查.gitignore
    check_gitignore()
    
    # 检查特定文件
    target_file = "119net_scraped/index.html"
    check_specific_file(target_file)
    
    # 检查Git配置
    check_git_config()
    
    # 提供解决方案
    suggest_solutions()

if __name__ == "__main__":
    main()
