#!/usr/bin/env python3
"""
修复所有页面的外部资源引用
实现完全本地化，不依赖外部库
"""

import os
import re
import glob

def fix_html_file(file_path):
    """修复单个HTML文件"""
    print(f"正在处理: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # 资源路径替换规则
        replacements = [
            # WordPress emoji脚本
            (r'window\._wpemojiSettings = \{[^}]+\};[^<]*!function\(e,a,t\)\{[^<]+\}\(window,document,window\._wpemojiSettings\);', 
             '// WordPress emoji settings removed for static site'),
            
            # WordPress主题图片路径
            (r'/wp-content/themes/busiprof/images/productbg\.jpg', 'images/servicebg2.jpg'),
            (r'/wp-content/themes/busiprof/images/datas1\.jpg', 'images/service3.jpg'),
            (r'/wp-content/themes/busiprof/images/([^"\']*\.(jpg|png|gif|svg))', r'images/\1'),
            
            # WordPress脚本和样式
            (r'/wp-includes/js/wp-emoji-release\.min\.js[^"\']*', ''),
            (r'/wp-includes/[^"\']*', ''),
            
            # 外部资源
            (r'https://s\.w\.org/[^"\']*', ''),
            (r'https://fonts\.googleapis\.com/[^"\']*', ''),
            (r'https://fonts\.gstatic\.com/[^"\']*', ''),
            (r'https://api\.w\.org/', ''),
            
            # 修复错误的链接
            (r'index\.htmlindex\.php[^"\']*', ''),
            (r'href="index\.html\?[^"]*"', 'href="index.html"'),
            
            # 移除WordPress特定的meta标签
            (r'<link rel="https://api\.w\.org/" href="[^"]*" />', ''),
            (r'<link rel="alternate" type="application/json" href="[^"]*" />', ''),
            (r'<link rel="alternate" type="application/json\+oembed" href="[^"]*" />', ''),
            (r'<link rel="alternate" type="text/xml\+oembed" href="[^"]*" />', ''),
        ]
        
        # 应用替换规则
        for pattern, replacement in replacements:
            new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            if new_content != content:
                changes_made += 1
                content = new_content
        
        # 如果有更改，保存文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已更新: {os.path.basename(file_path)} ({changes_made} 处更改)")
            return True
        else:
            print(f"⚪ 无需更新: {os.path.basename(file_path)}")
            return False
            
    except Exception as e:
        print(f"❌ 处理失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复所有页面的外部资源引用")
    print("=" * 50)
    
    # 查找所有HTML文件
    html_pattern = "119net_scraped/*.html"
    html_files = glob.glob(html_pattern)
    
    if not html_files:
        print("❌ 未找到HTML文件")
        return
    
    print(f"找到 {len(html_files)} 个HTML文件")
    
    updated_count = 0
    for html_file in html_files:
        if fix_html_file(html_file):
            updated_count += 1
    
    print(f"\n🎉 修复完成！")
    print(f"总计: {len(html_files)} 个文件")
    print(f"更新: {updated_count} 个文件")
    print(f"跳过: {len(html_files) - updated_count} 个文件")
    print("\n现在所有页面都已本地化，不再依赖外部资源。")

if __name__ == "__main__":
    main()
