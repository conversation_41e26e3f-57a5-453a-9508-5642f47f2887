#!/usr/bin/env python3
import os
import re

# 链接映射表
link_mappings = {
    'https://www.119.net/?page_id=317': 'building_fire_center.html',
    'https://www.119.net/?page_id=320': 'government_fire_center.html', 
    'https://www.119.net/?page_id=324': 'group_fire_center.html',
    'https://www.119.net/?page_id=322': 'smart_electrical.html',
    'https://www.119.net/?page_id=326': 'our_services.html',
    '/?page_id=67': 'success_cases.html',
    '/?page_id=61': 'industry_news.html',
    '/?page_id=63': 'about_tengyu.html',
    '/?page_id=59': 'technical_support.html',
    '/?page_id=65': 'contact_us.html',
    'https://www.119.net/?p=871': 'fire_hazard_rules.html',
    'https://www.119.net/?p=868': 'fire_statistics_rules.html',
    'https://www.119.net/?p=865': 'national_standard_gb4717.html',
    # 添加主页链接
    'https://www.119.net/': 'index.html',
    'https://www.119.net': 'index.html',
}

def update_file_links(filename):
    """更新单个文件中的链接"""
    print(f"正在更新: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # 替换所有映射的链接
        for old_link, new_link in link_mappings.items():
            if old_link in content:
                content = content.replace(old_link, new_link)
                changes_made += 1
                print(f"  替换: {old_link} -> {new_link}")
        
        # 如果有更改，保存文件
        if content != original_content:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ {filename} 已更新 ({changes_made} 处更改)")
        else:
            print(f"  {filename} 无需更改")
            
        return changes_made
        
    except Exception as e:
        print(f"✗ 更新 {filename} 时出错: {str(e)}")
        return 0

def main():
    """主函数"""
    print("开始更新所有HTML文件中的链接...")
    
    # 获取所有HTML文件
    html_files = [f for f in os.listdir('.') if f.endswith('.html')]
    
    total_changes = 0
    
    for filename in html_files:
        changes = update_file_links(filename)
        total_changes += changes
        print()
    
    print(f"=== 更新完成 ===")
    print(f"总共更新了 {total_changes} 个链接")
    print(f"处理了 {len(html_files)} 个HTML文件")

if __name__ == "__main__":
    main()