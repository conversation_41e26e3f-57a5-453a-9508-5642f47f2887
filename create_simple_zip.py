#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建119.net项目的简化zip包
"""

import os
import zipfile
import datetime

def create_simple_zip():
    """创建简化的zip包"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = "119net_website_{}.zip".format(timestamp)
    
    print("开始创建119.net网站包: {}".format(zip_filename))
    print("=" * 50)
    
    included_files = 0
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED, allowZip64=True) as zipf:
        # 要包含的文件和目录
        include_patterns = [
            '119net_scraped/*.html',
            '119net_scraped/css/',
            '119net_scraped/js/',
            '119net_scraped/images/',
            '119net_scraped/fonts/',
            '119net_scraped/data/',
            'README.md'
        ]
        
        # 排除的文件扩展名
        exclude_extensions = ['.py', '.pyc', '.pyo', '.pyd']
        
        # 遍历119net_scraped目录
        if os.path.exists('119net_scraped'):
            for root, dirs, files in os.walk('119net_scraped'):
                # 排除venv目录
                if 'venv' in dirs:
                    dirs.remove('venv')
                
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # 检查是否是Python文件
                    is_python_file = any(file.lower().endswith(ext) for ext in exclude_extensions)
                    
                    if not is_python_file:
                        # 计算在zip中的路径
                        arcname = file_path.replace('\\', '/')
                        try:
                            zipf.write(file_path, arcname)
                            included_files += 1
                            print("包含: {}".format(file_path))
                        except Exception as e:
                            print("跳过: {} (错误: {})".format(file_path, str(e)))
                    else:
                        print("排除Python文件: {}".format(file_path))
        
        # 添加README.md
        if os.path.exists('README.md'):
            zipf.write('README.md', 'README.md')
            included_files += 1
            print("包含: README.md")
    
    print("=" * 50)
    print("统计信息:")
    print("   包含文件: {}".format(included_files))
    
    if os.path.exists(zip_filename):
        print("   压缩包大小: {:.2f} MB".format(os.path.getsize(zip_filename) / 1024 / 1024))
        print("成功创建: {}".format(zip_filename))
        
        # 创建说明文件
        readme_content = """# 119.net 网站包

## 包信息
- 创建时间: {}
- 包含文件: {} 个
- 版本: {}

## 目录结构
- 119net_scraped/ - 网站主目录
  - index.html - 首页
  - css/ - 样式文件
  - js/ - JavaScript文件
  - images/ - 图片资源
  - fonts/ - 字体文件
  - data/ - 数据文件
  - *.html - 各个页面文件

## 部署说明
1. 解压到Web服务器目录
2. 确保服务器支持静态文件服务
3. 访问 119net_scraped/index.html 即可

## 技术特性
- 响应式设计
- 动态统计数据（调用生产API）
- 完整的页面导航
- 优化的资源加载

## 联系信息
如有问题，请联系技术支持。

---
生成时间: {}
""".format(
            datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S"),
            included_files,
            timestamp,
            datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        readme_filename = "119net_website_{}_README.txt".format(timestamp)
        with open(readme_filename, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("已创建说明文件: {}".format(readme_filename))
        print("打包完成！")
    else:
        print("创建zip文件失败！")

if __name__ == "__main__":
    create_simple_zip()
