@echo off
chcp 65001 >nul
title 119.net 服务器启动器

echo.
echo 🔥 119.net 服务器启动器
echo ================================
echo.

:menu
echo 请选择启动方式:
echo.
echo [1] Flask服务器 (推荐) - 端口5000
echo [2] Python内置服务器 - 端口8000  
echo [3] 自定义Flask端口
echo [4] 退出
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto flask_default
if "%choice%"=="2" goto simple_server
if "%choice%"=="3" goto flask_custom
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto menu

:flask_default
echo.
echo 🚀 启动Flask服务器 (端口5000)...
python start_server.py --type flask --install-deps
goto end

:simple_server
echo.
echo 🚀 启动Python内置服务器 (端口8000)...
python start_server.py --type simple
goto end

:flask_custom
echo.
set /p port="请输入端口号 (默认5000): "
if "%port%"=="" set port=5000
echo 🚀 启动Flask服务器 (端口%port%)...
python start_server.py --type flask --port %port% --install-deps
goto end

:end
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:exit
echo.
echo 👋 再见！
timeout /t 2 >nul
exit
