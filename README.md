# 🔥 119.net 网站服务器

这是一个用于提供119.net静态网站服务的Python Web服务器项目。

## 📁 项目结构

```
119.net/
├── 119net_scraped/          # 静态网站文件
│   ├── index.html           # 主页
│   ├── css/                 # 样式文件
│   ├── js/                  # JavaScript文件
│   ├── images/              # 图片文件
│   ├── fonts/               # 字体文件
│   └── *.html               # 其他页面
├── app.py                   # Flask Web服务器
├── start_server.py          # 服务器启动脚本
├── start_server.bat         # Windows批处理启动器
└── README.md                # 说明文档
```

## 🚀 快速启动

### 方法1: 使用批处理文件 (Windows推荐)

双击运行 `start_server.bat`，然后按照菜单提示选择启动方式。

### 方法2: 使用Python启动脚本

```bash
# 启动Flask服务器 (推荐)
python start_server.py --type flask --install-deps

# 启动Python内置HTTP服务器
python start_server.py --type simple

# 自定义端口启动Flask服务器
python start_server.py --type flask --port 8080
```

### 方法3: 直接运行Flask应用

```bash
# 安装依赖
pip install flask

# 启动Flask服务器
python app.py
```

### 方法4: 使用Python内置HTTP服务器

```bash
cd 119net_scraped
python -m http.server 8000
```

## 🌐 访问地址

### Flask服务器 (默认端口5000)
- **主页**: http://localhost:5000
- **管理面板**: http://localhost:5000/admin
- **API接口**: http://localhost:5000/api/pages

### Python内置服务器 (默认端口8000)
- **主页**: http://localhost:8000

## 📄 页面路由

| 页面名称 | URL路径 | 文件名 |
|---------|---------|--------|
| 首页 | `/` | index.html |
| 关于腾御 | `/about` | about_tengyu.html |
| 我们的服务 | `/services` | our_services.html |
| 楼宇消防接入中心 | `/building-fire` | building_fire_center.html |
| 政府消防数据中心 | `/government-fire` | government_fire_center.html |
| 集团消防数据中心 | `/group-fire` | group_fire_center.html |
| 智慧用电 | `/smart-electrical` | smart_electrical.html |
| 成功案例 | `/success-cases` | success_cases.html |
| 行业动态 | `/industry-news` | industry_news.html |
| 技术支持 | `/technical-support` | technical_support.html |
| 联系我们 | `/contact` | contact_us.html |
| 火灾隐患判定规则 | `/fire-hazard-rules` | fire_hazard_rules.html |
| 火灾统计规则 | `/fire-statistics-rules` | fire_statistics_rules.html |
| 国家标准GB4717 | `/national-standard` | national_standard_gb4717.html |

## 🔧 功能特性

### Flask服务器特性
- ✅ 支持所有静态资源 (CSS, JS, 图片, 字体)
- ✅ 友好的URL路由
- ✅ 管理面板界面
- ✅ JSON API接口
- ✅ 调试模式支持
- ✅ 自动重载

### Python内置服务器特性
- ✅ 简单快速
- ✅ 无需额外依赖
- ✅ 适合快速预览

## 📋 系统要求

- Python 3.6 或更高版本
- Flask (Flask服务器需要)

## 🛠️ 安装依赖

```bash
# 安装Flask
pip install flask

# 或使用启动脚本自动安装
python start_server.py --type flask --install-deps
```

## 🔍 故障排除

### 问题1: 静态文件目录不存在
**错误**: `错误: 静态文件目录 '119net_scraped' 不存在`

**解决**: 确保已经运行了网站抓取脚本，生成了静态文件。

### 问题2: Flask未安装
**错误**: `Flask未安装`

**解决**: 
```bash
pip install flask
```

### 问题3: 端口被占用
**错误**: `Address already in use`

**解决**: 
- 更换端口: `python start_server.py --type flask --port 8080`
- 或停止占用端口的程序

### 问题4: 权限错误
**错误**: `Permission denied`

**解决**: 
- 使用管理员权限运行
- 或使用大于1024的端口号

## 📞 技术支持

如果遇到问题，请检查:
1. Python版本是否符合要求
2. 静态文件目录是否存在
3. 依赖是否正确安装
4. 端口是否被占用

## 📝 更新日志

- v1.0.0: 初始版本，支持Flask和Python内置服务器
- 支持所有静态资源文件
- 提供管理面板和API接口
- 包含Windows批处理启动器
