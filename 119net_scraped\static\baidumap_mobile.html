<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
    <style type="text/css">
        body, html {width: 100%;height: 100%;margin:0;font-family:"微软雅黑";}
        #allmap{width:100%;height:980px;}
        p{margin-left:5px; font-size:14px;}
    </style>
    <title>百度地图</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=LmKi9dK3iwasAjGlRiMIAnNqDsRZ9duk&s=1"></script>
</head>
<body>
<div id="allmap"></div>
</body>
</html>
<script type="text/javascript">
    // 百度地图API功能
    var sContent =
        "<p style='margin:0;line-height:1.5;font-size:13px;text-indent:2em'>上海市 杨浦区 鞍山路5号</p>" +
        "<p style='margin:0;line-height:1.5;font-size:13px;text-indent:2em'>杨浦商城18楼</p>" +
        "<p style='margin:0;line-height:1.5;font-size:13px;text-indent:2em'>021-66690267</p>" +
        "</div>";
    var map = new BMap.Map("allmap");
    // var point = new BMap.Point(121.500281, 31.274155);
    var point = new BMap.Point(121.519619, 31.280174);
    // var marker = new BMap.Marker(point);
    var myIcon = new BMap.Icon("./images/position.png", new BMap.Size(60, 120), {  
        imageSize: new BMap.Size(60, 120)
        // offset: new BMap.Size(100, 170), // 指定定位位置  
        // imageOffset: new BMap.Size(0, -170) // 设置图片偏移  
    });    
    var marker=new BMap.Marker(point,{icon:myIcon});  

    const label = new BMap.Label(
      '上海市 杨浦区 鞍山路5号<br/>杨浦商城18楼<br/>4006659119<br/>业务部13761916848（同微信）',
      { offset: new BMap.Size( -80, 120 ) }
    )
    label.setStyle({
        color: "#000",
        fontSize: "20px",
        width: '340px',
        height: "168px",
        lineHeight: "36px",
        fontFamily:"微软雅黑",
        border:0,
        background: '#fff',
        paddingLeft: '16px',
        paddingTop: '16px',
    });
    marker.setLabel(label);

    map.centerAndZoom(point, 20);
    map.panBy(0,-300)
    map.addOverlay(marker);

</script>
