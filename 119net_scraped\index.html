<!DOCTYPE html>

<html lang="zh-CN">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1" name="viewport"/>
<meta content="ie=edge" http-equiv="x-ua-compatible"/>
<title>九消消防物联网 – 智慧消防 – 腾御科技</title>
<meta content="max-image-preview:large" name="robots">



<link href="index.html?feed=rss2" rel="alternate" title="九消消防物联网 » Feed" type="application/rss+xml"/>
<link href="index.html?feed=comments-rss2" rel="alternate" title="九消消防物联网 » 评论Feed" type="application/rss+xml"/>
<!-- WordPress emoji support completely disabled for static site -->
<!--
<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.7.2"}};
			!function(e,a,t){var n,r,o,i=a.createElement("canvas"),p=i.getContext&&i.getContext("2d");function s(e,t){var a=String.fromCharCode;p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,e),0,0);e=i.toDataURL();return p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,t),0,0),e===i.toDataURL()}function c(e){var t=a.createElement("script");t.src=e,t.defer=t.type="text/javascript",a.getElementsByTagName("head")[0].appendChild(t)}for(o=Array("flag","emoji"),t.supports={everything:!0,everythingExceptFlag:!0},r=0;r<o.length;r++)t.supports[o[r]]=function(e){if(!p||!p.fillText)return!1;switch(p.textBaseline="top",p.font="600 32px Arial",e){case"flag":return s([127987,65039,8205,9895,65039],[127987,65039,8203,9895,65039])?!1:!s([55356,56826,55356,56819],[55356,56826,8203,55356,56819])&&!s([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]);case"emoji":return!s([55357,56424,8205,55356,57212],[55357,56424,8203,55356,57212])}return!1}(o[r]),t.supports.everything=t.supports.everything&&t.supports[o[r]],"flag"!==o[r]&&(t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&t.supports[o[r]]);t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&!t.supports.flag,t.DOMReady=!1,t.readyCallback=function(){t.DOMReady=!0},t.supports.everything||(n=function(){t.readyCallback()},a.addEventListener?(a.addEventListener("DOMContentLoaded",n,!1),e.addEventListener("load",n,!1)):(e.attachEvent("onload",n),a.attachEvent("onreadystatechange",function(){"complete"===a.readyState&&t.readyCallback()})),(n=t.source||{}).concatemoji?c(n.concatemoji):n.wpemoji&&n.twemoji&&(c(n.twemoji),c(n.wpemoji)))}(window,document,window._wpemojiSettings);
		</script>
-->
<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
<link href="css/style_1.css" id="wp-block-library-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_2.css" id="busiprof-fonts-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_3.css" id="busiprof-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_4.css" id="bootstrap-css-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_5.css" id="busiporf-custom-css-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_6.css" id="flexslider-css-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_7.css" id="busiporf-Droid-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_8.css" id="busiporf-Montserrat-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_9.css" id="busiporf-Droid-serif-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_10.css" id="font-awesome-css-css" media="all" rel="stylesheet" type="text/css"/>
<link href="css/style_11.css" id="lazyprof-parent-style-css" media="all" rel="stylesheet" type="text/css"/>
<script id="jquery-js" src="js/script_1.js" type="text/javascript"></script>
<script id="bootstrap-js-js" src="js/script_2.js" type="text/javascript"></script>
<script id="busiporf-custom-js-js" src="js/script_3.js" type="text/javascript"></script>
<!-- External script disabled for static site -->
<!-- <script id="xiaofang-js" src="https:" type="text/javascript"></script> -->
<link href="index.htmlindex.php?rest_route=/" rel="https://api.w.org/"/>

<meta content="WordPress 5.7.2" name="generator">
<!-- Custom Logo: hide header text -->
<style id="custom-logo-css" type="text/css">
			.site-title, .site-description {
				position: absolute;
				clip: rect(1px, 1px, 1px, 1px);
			}
		</style>
<style type="text/css">.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}</style><!-- Logo icon disabled - file not found -->
<!-- <link href="images/logo1.png" rel="icon" sizes="32x32"/> -->
<!-- <link href="images/logo1.png" rel="icon" sizes="192x192"/> -->
<!-- <link href="images/logo1.png" rel="apple-touch-icon"/> -->
<!-- <meta content="images/logo1.png" name="msapplication-TileImage"> -->
</meta></meta></meta></head>
<body class="home blog wp-custom-logo">
<div class="site" id="page">
<!-- <a class="skip-link busiprof-screen-reader" href="#content">Skip to content</a> -->
<!-- Navbar -->
<nav class="navbar navbar-default">
<!-- <div class="container"> -->
<div class="maincontainer">
<!-- Brand and toggle get grouped for better mobile display -->
<div class="navbar-header">
<span class="navbar-brand"><a aria-current="page" class="custom-logo-link" href="index.html" rel="home"><img alt="九消消防物联网" class="custom-logo" height="42" src="images/image_1.png" width="91"/></a></span> <div class="custom-logo-link-url">
<h1 class="site-title"><a class="navbar-brand" href="index.html">九消消防物联网</a>
</h1>
<p class="site-description">智慧消防 – 腾御科技</p>
</div>
<button class="navbar-toggle collapsed" data-target="#bs-example-navbar-collapse-1" data-toggle="collapse" type="button">
<span class="sr-only">Toggle navigation</span>
<span class="icon-bar"></span>
<span class="icon-bar"></span>
<span class="icon-bar"></span>
</button>
</div>
<!-- Collect the nav links, forms, and other content for toggling -->
<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
<ul class="nav navbar-nav navbar-right" id="menu-menu"><li class="menu-item menu-item-type-custom menu-item-object-custom current-menu-item menu-item-288 active" id="menu-item-288"><a href="index.html">首页<img class="arrow" src="images/image_2.png"/></a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-34 dropdown" id="menu-item-34"><a>产品与服务<img class="arrow" src="images/image_3.png"/><span class="caret"></span></a>
<ul class="dropdown-menu">
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-328" id="menu-item-328"><a href="building_fire_center.html">楼宇消防接入中心<img class="arrow" src="images/image_4.png"/></a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-329" id="menu-item-329"><a href="government_fire_center.html">政府消防数据中心<img class="arrow" src="images/image_5.png"/></a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-331" id="menu-item-331"><a href="group_fire_center.html">集团消防数据中心<img class="arrow" src="images/image_6.png"/></a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-330" id="menu-item-330"><a href="smart_electrical.html">智慧用电<img class="arrow" src="images/image_7.png"/></a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-332" id="menu-item-332"><a href="our_services.html">我们的服务<img class="arrow" src="images/image_8.png"/></a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-738" id="menu-item-738"><a href="https://book.yunzhan365.com/bookcase/ukwe/index.html">产品资料<img class="arrow" src="images/image_9.png"/></a></li>
</ul>
</li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-36" id="menu-item-36"><a href="success_cases.html">成功案例<img class="arrow" src="images/image_10.png"/></a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37" id="menu-item-37"><a href="industry_news.html">行业动态<img class="arrow" src="images/image_11.png"/></a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-38" id="menu-item-38"><a href="about_tengyu.html">关于腾御<img class="arrow" src="images/image_12.png"/></a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-39" id="menu-item-39"><a href="technical_support.html">技术支持<img class="arrow" src="images/image_13.png"/></a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-40" id="menu-item-40"><a href="contact_us.html">联系我们<img class="arrow" src="images/image_14.png"/></a></li>
</ul>
</div>
</div>
</nav>
<!-- End of Navbar -->
<!-- <div style="height: 96px;width: 100%;"></div> -->
<style>
	.arrow {display: none;}
	.container {width: 1200px;padding:0;}
	.maincontainer {width:100%;padding:0 100px;}
	.custom-logo {width: 91px;height: 42px;}
	/* #page {position: relative;}
	.navbar {position: fixed;left:0;right:0;z-index: 999999999;} */
	@media screen and (max-width: 1600px) {
		.maincontainer {width:100%;padding:0 24px;}
	}
	@media screen and (max-width: 1200px) {
		.container {width: 100%;}
	}
	@media screen and (max-width: 1100px) {
		.container {width: auto;}
		.maincontainer {width:100%;padding:0 14px 0 24px;}
		.nav > li {z-index: 999;}
		.navbar-nav>li a{display: flex;align-items: center;justify-content: space-between;}
		.navbar-nav li {border-bottom: 1px solid rgba(0,0,0,.05);}
		.navbar-nav li .arrow {width: 16px;height: 16px; display: inline-block}
		.navbar-nav li.dropdown>a .arrow {display: none;}
		.caret {margin-right: 20px;margin-top: 2px;margin-left:0;border-top:6px dashed;border-right: 6px solid transparent;border-left:6px solid transparent;}
		.navbar-nav .dropdown-menu {display: block;background: #F1F1F1;border:0;box-shadow:none;-webkit-box-shadow:none;} 
		.dropdown-menu > li > a {background: #f1f1f1 !important;color: #000;border-bottom:0;padding: 15px;}
		.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {color: #337ab7;}

		.navbar-collapse {position: absolute;right: 0px;left: 0px;background: rgb(255, 255, 255);z-index:9999999;}

		.navbar {min-height: 56px;}
		.navbar-toggle {margin: 8px 15px 8px 0px;}
		.custom-logo {width: 52px;height: 24px;}
		.navbar-default .navbar-toggle {border:0;}

		/* .navbar-toggle {margin: 12px 15px 8px 0px;}
		.logo_imgae {width:96px !important;height: 34px !important;}  */
	}
	
</style><!-- Slider Section of Index Page -->
<div id="content">
<!-- footer Section of index blog -->
</div>
<!-- Projects Section of index Page -->
<!-- footer Section of index blog -->
<!-- footer Section of index Testimonial -->
<div class="fan_banner">
<div class="bannertxtbox">
<div class="title">
<div class="titletxt">
<p>智 慧 消 防</p>
<p>九 消 护 航</p>
</div>
</div>
<div class="desc">
<p>
腾御(上海)信息科技有限公司，是一家专注于物联网技术应用于消防、安全、节能、管理等领域的高科技企业。

</p>
</div>
<div class="numbox">
<ul>
<li>
<div class="num"><p class="numberRun2 numbers"></p><p class="txt">幢</p></div>
<p>保护的建筑数</p>
</li>
<li>
<div class="num"><p class="numberRun3 numbers"></p><p class="txt">个</p></div>
<p>监测点位数</p>
</li>
<li>
<div class="num"><p class="numberRun4 numbers"></p><p class="txt">件</p></div>
<p>处理风险事件</p>
</li>
</ul>
</div>
</div>
</div>
<style>
    /* .fan_banner{height:936px;background:url(index.htmlwp-content/plugins/webriti-companion/inc/busiprof/img/home_slide.jpg) repeat-x center;overflow:hidden;} */
    @font-face {
        font-family: 'gown';
        src: url("fonts/Graficz_OT_W00_Normal.ttf");
    }
    .fan_banner{height:730px;background:url(images/banner.jpg) no-repeat center;background-size: cover;overflow:hidden;}
    .bannertxtbox {margin:0 auto;margin-top: 130px;}
    .bannertxtbox .title .titletxt{display: flex;flex-wrap: wrap;justify-content: center;}
    .bannertxtbox .title .titletxt p{font-size: 72px;color: #fff;padding:0 30px;}
    .bannertxtbox .desc p{width: 592px;margin:60px auto;color: #fff;font-size:24px;line-height: 42px;}
    .bannertxtbox .numbox {width: 1200px;text-align: center;margin: 110px auto 0;}
    .bannertxtbox .numbox ul{display: flex;flex-wrap: wrap;}
    .bannertxtbox .numbox ul li {flex: 1;}
    .bannertxtbox .numbox p{font-size: 36px;color: #fff;}
    .bannertxtbox .numbox .num{color: #fff;font-family:'SourceHanSansCN';height: 70px;display: flex;align-items: flex-end;justify-content: center;}
    .bannertxtbox .numbox .num p span {font-size: 80px;font-family: 'gown';}
    .bannertxtbox .numbox .num p.numbers {height: 70px;}
    .bannertxtbox .numbox .num p.txt {font-size: 36px;margin-left: 10px;}
    @media screen and (max-width: 1200px) {
        .bannertxtbox .numbox {width: 100%;}
    }
    @media screen and (max-width: 768px) {
        .fan_banner {height: 583px;}
        .bannertxtbox {margin-top: 48px;}
        .bannertxtbox .title .titletxt p{font-size: 50px;line-height: 62px;}
        .bannertxtbox .desc p {width: 246px;margin:16px auto 0;font-size: 14px;line-height: 24px;}
        .bannertxtbox .numbox{margin-top: 0;}
        .bannertxtbox .numbox ul{width: 246px;margin:36px auto;display: block;}
        .bannertxtbox .numbox ul li{padding:0 0 14px;}

        .bannertxtbox .numbox p{font-size: 16px;}
        .bannertxtbox .numbox .num {height: 42px;}
        .bannertxtbox .numbox .num p span {font-size: 40px;}
        .bannertxtbox .numbox .num p.numbers {height: 42px;}
        .bannertxtbox .numbox .num p.txt {font-size: 16px;margin-left: 5px;}

        /* .bannertxtbox .numbox p{font-size: 16px;}
        .bannertxtbox .numbox p.num {height: 60px;}
        .bannertxtbox .numbox p.num span {font-size: 48px;}
        .bannertxtbox .numbox p.num span.txt {font-size: 16px;} */
    }
</style>
<!-- <script src="js/script_5.js"></script> -->
<script src="js/script_5.js"></script>
<!---->
<script>
    /**
     *  by Mantou qq:676015863
     *  数字滚动插件 v1.0
     */
    ;(function($) {
        $.fn.numberAnimate = function(setting) {
            var defaults = {
                speed : 1000,//动画速度
                num : "", //初始化值
                iniAnimate : true, //是否要初始化动画效果
                symbol : '',//默认的分割符号，千，万，千万
                dot : 0 //保留几位小数点
            }
            //如果setting为空，就取default的值
            var setting = $.extend(defaults, setting);

            //如果对象有多个，提示出错
            if($(this).length > 1){
                alert("just only one obj!");
                return;
            }

            //如果未设置初始化值。提示出错
            // if(setting.num == ""){
            //     alert("must set a num!");
            //     return;
            // }
            var nHtml = '<div class="mt-number-animate-dom" data-num="{{num}}">\
            <span class="mt-number-animate-span">0</span>\
            <span class="mt-number-animate-span">1</span>\
            <span class="mt-number-animate-span">2</span>\
            <span class="mt-number-animate-span">3</span>\
            <span class="mt-number-animate-span">4</span>\
            <span class="mt-number-animate-span">5</span>\
            <span class="mt-number-animate-span">6</span>\
            <span class="mt-number-animate-span">7</span>\
            <span class="mt-number-animate-span">8</span>\
            <span class="mt-number-animate-span">9</span>\
            <span class="mt-number-animate-span">.</span>\
          </div>';

            //数字处理
            var numToArr = function(num){
                num = parseFloat(num).toFixed(setting.dot);
                if(typeof(num) == 'number'){
                    var arrStr = num.toString().split("");
                }else{
                    var arrStr = num.split("");
                }
                //console.log(arrStr);
                return arrStr;
            }

            //设置DOM symbol:分割符号
            var setNumDom = function(arrStr){
                var shtml = '<div class="mt-number-animate">';
                for(var i=0,len=arrStr.length; i<len; i++){
                    if(i != 0 && (len-i)%3 == 0 && setting.symbol != "" && arrStr[i]!="."){
                        shtml += '<div class="mt-number-animate-dot">'+setting.symbol+'</div>'+nHtml.replace("{{num}}",arrStr[i]);
                    }else{
                        shtml += nHtml.replace("{{num}}",arrStr[i]);
                    }
                }
                shtml += '</div>';
                return shtml;
            }

            //执行动画
            var runAnimate = function($parent){
                $parent.find(".mt-number-animate-dom").each(function() {
                    var num = $(this).attr("data-num");
                    num = (num=="."?10:num);
                    var spanHei = $(this).height()/11; //11为元素个数
                    var thisTop = -num*spanHei+"px";
                    if(thisTop != $(this).css("top")){
                        if(setting.iniAnimate){
                            //HTML5不支持
                            if(!window.applicationCache){
                                $(this).animate({
                                    top : thisTop
                                }, setting.speed);
                            }else{
                                $(this).css({
                                    'transform':'translateY('+thisTop+')',
                                    '-ms-transform':'translateY('+thisTop+')',   /* IE 9 */
                                    '-moz-transform':'translateY('+thisTop+')',  /* Firefox */
                                    '-webkit-transform':'translateY('+thisTop+')', /* Safari 和 Chrome */
                                    '-o-transform':'translateY('+thisTop+')',
                                    '-ms-transition':setting.speed/1000+'s',
                                    '-moz-transition':setting.speed/1000+'s',
                                    '-webkit-transition':setting.speed/1000+'s',
                                    '-o-transition':setting.speed/1000+'s',
                                    'transition':setting.speed/1000+'s'
                                });
                            }
                        }else{
                            setting.iniAnimate = true;
                            $(this).css({
                                top : thisTop
                            });
                        }
                    }
                });
            }

            //初始化
            var init = function($parent){
                //初始化
                $parent.html(setNumDom(numToArr(setting.num)));
                runAnimate($parent);
            };

            //重置参数
            this.resetData = function(num){
                var newArr = numToArr(num);
                var $dom = $(this).find(".mt-number-animate-dom");
                if($dom.length < newArr.length){
                    $(this).html(setNumDom(numToArr(num)));
                }else{
                    $dom.each(function(index, el) {
                        $(this).attr("data-num",newArr[index]);
                    });
                }
                runAnimate($(this));
            }
            //init
            init($(this));
            return this;
        }
    })(jQuery);

    (function($){
        function fn2() {
            $.ajax({
                method : 'GET',
                // data:{"id":1}, //以键/值对的形式
                headers: {
                    'token': 'TEGNYUkeji2019'
                },
                // url : 'http://192.168.5.129:8361/admin/statistics/getHomePageInfo',
                url : 'data/statistics.json',
                success : function (res) {
                    nums2 = res.data.buildNum;
                    nums3 = res.data.positionNum;
                    nums4 = res.data.riskNum;
                    numRun2.resetData(nums2);
                    numRun3.resetData(nums3);
                    numRun4.resetData(nums4);
                }
            });
        }

        //初始化
        var nums2 = 0;
        var nums3 = 0;
        var nums4 = 0;
        var numRun2 = $(".numberRun2").numberAnimate({num: nums2, speed:2000});
        var numRun3 = $(".numberRun3").numberAnimate({num: nums3, speed:2000});
        var numRun4 = $(".numberRun4").numberAnimate({num: nums4, speed: 2000});
        fn2();
        setInterval(function(){
            fn2();
        }, 10000);
        
    })(jQuery);
</script>
<style>
    .billboard-con {
        position: absolute;
        z-index: 2;
        height: 128px;
        top: 50%;
        margin-top: -84px;
        width: 984px;
        left: 50%;
        margin-left: -492px;
        transform: translateZ(0);
    }
    .cssanimations .billboard.show .man-sloagn, .cssanimations .billboard.show .sub-title, .cssanimations .billboard.show .play-video, .cssanimations .billboard.show .sub-title2, .cssanimations .billboard.show .sub-mess {
        animation: 1.5s slideUp cubic-bezier(0.445, 0.05, 0.55, 0.95) both;
        -moz-animation: 1.5s slideUp cubic-bezier(0.445, 0.05, 0.55, 0.95) both;
        -webkit-animation: 1.5s slideUp cubic-bezier(0.445, 0.05, 0.55, 0.95) both;
        -o-animation: 1.5s slideUp cubic-bezier(0.445, 0.05, 0.55, 0.95) both;
    }
    .cssanimations .billboard .man-sloagn, .cssanimations .billboard .sub-title, .cssanimations .billboard .play-video, .cssanimations .billboard .sub-title2, .cssanimations .billboard .sub-mess {
        opacity: 0;
    }
    .cssanimations .billboard.show .sub-title {
        animation-delay: .25s;
    }
    .cssanimations .billboard.show .sub-title2 {
        animation-delay: .45s;
    }
    .mt-number-animate{ font-family: '微软雅黑'; line-height: 70px; height: 70px;/*设置数字显示高度*/; font-size: 26px;/*设置数字大小*/ overflow: hidden; display: inline-block; position: relative; }
    .mt-number-animate .mt-number-animate-dot{ width: 15px;/*设置分割符宽度*/ line-height: 30px; float: left; text-align: center;}
    .mt-number-animate .mt-number-animate-dom{ width: 30px;/*设置单个数字宽度*/ text-align: center; float: left; position: relative; top: 0;}
    .mt-number-animate .mt-number-animate-dom .mt-number-animate-span{ width: 100%; float: left;}
    @media screen and (max-width: 768px) {
        .mt-number-animate {line-height: 42px; height: 42px;}
        .mt-number-animate .mt-number-animate-dom {width: 20px;}
    }
</style>
<section id="core">
<div class="container">
<div class="categroyname">
<span>核心竞争力</span>
</div>
<div class="row image-parallax fullwidthrow corecontent">
<div class="prallax-wrap">
<div class="parallax-contents" style="padding: 0px 0px 48px; color: #373737; text-align: left;">
<div class="nx-row">
<div class="nx-column nx-column-size-1-3">
<div class="nx-column-inner nx-clearfix">
<img alt="" src="images/image_15.png"/>
<b>全感知</b>
<p>通过各种传感器感知建筑状态</p>
</div>
</div>
<div class="nx-column nx-column-size-1-3">
<div class="nx-column-inner nx-clearfix">
<img alt="" src="images/image_16.png"/>
<b>管执行</b>
<p>通过感知数据推动运维执行</p>
</div>
</div>
<div class="nx-column nx-column-size-1-3">
<div class="nx-column-inner nx-clearfix">
<img alt="" src="images/image_17.png"/>
<b>重分析</b>
<p>通过分析数据改进工作方法</p>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
</section>
<style>
    #core {height: 631px;background: #fff;}
    .categroyname {font-size: 48px;font-weight:bold;color: #000001;text-align: center;margin-top: 80px;}
    .corecontent .nx-column-inner{text-align: center;width: 400px;margin-top: 89px;}
    .corecontent img{width: 80px;height: 80px;margin:0 auto;}
    .corecontent b{font-size: 36px;color: #000;display: block;margin-top: 40px;}
    .corecontent p{font-size: 18px;color: #000;margin-top: 30px;}
    .coreinfo {font-size: 12px;}
    @media screen and (max-width: 768px) {
        #core{height: 1033px;}
        .categroyname {font-size: 32px;margin: 48px auto;}
        .corecontent .nx-column {overflow: hidden;}
        .corecontent .nx-column-inner{margin-top: 64px;margin-bottom: 71px;width: 100%;}
        .corecontent img{width: 48px;height: 48px;margin:0 auto;}   
        .corecontent b{font-size: 24px;margin-top: 24px;}
        .corecontent p{font-size: 14px;margin-top: 18px;}
    }
    
</style>
<section id="successcase">
<div class="container">
<div class="categroyname">
<span>成功案例</span>
</div>
<div class="caseitem">
<div class="caseinfo">
<div class="caseimg" style="background: url(images/bg_image_429.jpg) no-repeat center;background-size:cover;"></div>
<div class="infobottom">
<p><a>黄浦消防支队</a></p>
<p class="tag"><a>
                                政府单位                            </a></p>
</div>
</div>
<div class="caseinfo">
<div class="caseimg" style="background: url(images/bg_image_130.jpg) no-repeat center;background-size:cover;"></div>
<div class="infobottom">
<p><a>静安消防支队</a></p>
<p class="tag"><a>
                                政府单位                            </a></p>
</div>
</div>
<div class="caseinfo">
<div class="caseimg" style="background: url(images/bg_image_852.jpg) no-repeat center;background-size:cover;"></div>
<div class="infobottom">
<p><a>上海 张江（集团）有限公司</a></p>
<p class="tag"><a>
                                集团企业                            </a></p>
</div>
</div>
</div>
<div class="more"><a href="success_cases.html"><button>查看更多</button></a></div>
</div>
</section>
<style>
    #successcase {width: 100%;background-color: #F6F6F6;}
    .caseitem {margin-top: 80px;display: flex;}
    .caseinfo{background: #fff;margin-right: 27px;flex: 1;}
    .caseinfo:last-child {margin-right:0;}
    .caseinfo .infobottom {padding: 40px 0;}
    .caseinfo .infobottom p {text-align: center;}
    .caseinfo .infobottom p:first-child a{font-size: 32px;font-weight: 700;color: #000;}
    .caseinfo .infobottom p.tag{margin-top: 18px;}
    .caseinfo .infobottom p.tag a{font-size: 18px;color: #000;}
    .caseimg { width: 382px;height: 297px; }
    .more {text-align: center;margin-top: 80px;margin-bottom: 80px;}
    .more button {display: inline-block;width: 240px;height: 68px;font-size: 18px;background: #F6F6F6;border:1px solid #009AFF;color: #000;}
    .more button:hover {background: #fff;}
    .more button a {color: #000;}
    @media (max-width: 1200px) and (min-width: 768px) {
        .caseimg { width: 100%;height: 240px; }
    }
    @media screen and (max-width: 768px) {
        .caseitem {display: block;margin-top: 0;}
        .caseinfo {margin:0 24px;margin-bottom: 25px;}
        .caseinfo:last-child {margin:0 24px;}
        .caseinfo .infobottom {padding: 16px 24px 24px;}
        .caseinfo .infobottom p {text-align: center;}
        .caseinfo .infobottom p:first-child a{font-size: 18px;font-weight: 700;color: #000;}
        .caseinfo .infobottom p.tag{margin: 16px 0 0;}
        .caseinfo .infobottom p.tag a{font-size: 14px;}
        .caseimg { width: 100%;height: 255px; }
        .more {margin: 48px auto;}
        .more button {width: 177px;height: 32px;font-size: 16px;background: #F6F6F6;border:1px solid #009AFF;}
        .more button a {color: #000;}
    }

</style>
<section id="news">
<div class="container">
<div class="categroyname">
<span>行业动态</span>
</div>
<div class="newsitem">
<div class="newsinfo">
<div class="newsimg" style="background: url(images/bg_image_372.png) no-repeat center;background-size:cover;"></div>
<div class="infobox">
<p class="title"><a href="fire_hazard_rules.html" target="_blank">新版《重大火灾隐患判定规则》发布</a> </p>
<div class="part"><p>重大火灾隐患判定事关人民群众生命财产安全和切身利益。据介绍，新修订的《重大火灾隐患判定规则》强制性国家标准，建立了火灾隐患分级判定体系，通过量化指标和动态评估模型，实现隐患早识别、早预警，筑牢消防安全防线。</p></div>
<div class="detail"><a href="fire_hazard_rules.html" target="_blank"><button>查看更多</button></a></div>
</div>
</div>
<div class="newsinfo">
<div class="newsimg" style="background: url(images/bg_image_43.png) no-repeat center;background-size:cover;"></div>
<div class="infobox">
<p class="title"><a href="fire_statistics_rules.html" target="_blank">新《火灾统计管理规定》今年5月1日起施行</a> </p>
<div class="part"><p>近日，国家消防救援局、公安部、应急管理部、国家能源局、国家林业和草原局、中国民用航空局、国家矿山安全监察局联合印发《火灾统计管理规定》，自2025年5月1日起施行。1996年由公安部、原劳动部和国家统计局联合印发的《火灾统计管理规定》同步废止。</p></div>
<div class="detail"><a href="fire_statistics_rules.html" target="_blank"><button>查看更多</button></a></div>
</div>
</div>
<div class="newsinfo">
<div class="newsimg" style="background: url(images/bg_image_582.png) no-repeat center;background-size:cover;"></div>
<div class="infobox">
<p class="title"><a href="national_standard_gb4717.html" target="_blank">新版国家标准GB 4717-2024《火灾报警控制器》2025年5月1日起正式实施！</a> </p>
<div class="part"><p>本文件由中华人民共和国应急管理部提出并归口。</p></div>
<div class="detail"><a href="national_standard_gb4717.html" target="_blank"><button>查看更多</button></a></div>
</div>
</div>
</div>
<div class="more temore"><a href="industry_news.html"><button>更多动态</button></a></div>
</div>
</section>
<style>
    #news {background: #fff;}
    #news .newsitem {display: flex;margin-top: 80px;}
    #news .newsitem .newsinfo {margin-right: 27px;background: #F6F6F6;flex: 1;}'
    #news .newsitem .newsinfo:last-child {margin-right:0;}
    #news .newsitem .newsinfo .newsimg {height: 297px;width: 382px;}
    #news .newsitem .newsinfo .infobox {padding: 48px;}
    #news .newsitem .newsinfo .infobox .title a{color: #000;font-size: 32px;font-weight: 700;}
    #news .newsitem .newsinfo .infobox .part p{margin-top: 24px;font-size: 18px;color: #000;overflow: hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:4;-webkit-box-orient: vertical;line-height: 28px;height:112px;}
    #news .newsitem .newsinfo .infobox .detail {margin-top: 24px;}
    #news .newsitem .newsinfo .infobox .detail button {display: inline-block;width: 190px;height: 48px;font-size: 18px;background: #F6F6F6;border:1px solid #009AFF;color: #000;}
    #news .newsitem .newsinfo .infobox .detail button:hover {background: #fff;}
    #news .newsitem .newsinfo .infobox .detail button a {color: #000;font-size: 16px;}
    #news .more button{background: #fff;color: #000;}
    #news .more button:hover {background: #f6f6f6;}
    @media (max-width: 1200px) and (min-width: 768px) {
        #news .newsitem .newsinfo .newsimg {height: 260px;width: 100%;}
    }
    @media screen and (max-width: 768px) {
        #news .newsitem {flex-wrap: wrap;margin-top: 0;}
        #news .newsitem .newsinfo {margin:0 24px;margin-bottom: 24px;}
        #news .newsitem .newsinfo:last-child {margin-bottom:0;}
        #news .newsitem .newsinfo .newsimg {width: 100%;height: 255px;}

        #news .newsitem .newsinfo .infobox {padding: 16px 24px 24px;}
        #news .newsitem .newsinfo .infobox .title {text-align: center;}
        #news .newsitem .newsinfo .infobox .title a{font-size: 16px;font-weight: 700;}
        #news .newsitem .newsinfo .infobox .part p{margin-top: 16px;font-size: 14px;color: #000;overflow: hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient: vertical;line-height: 24px;height:72px;}
        #news .newsitem .newsinfo .infobox .detail {margin-top: 16px;}
        #news .newsitem .newsinfo .infobox .detail button {display: block;width: 177px;height: 32px;font-size: 16px;margin:16px auto 0;}
        #news .newsitem .newsinfo .infobox .detail button a {color: #000;}
        .temore button {border: 0;background: #fff;}
        .temore button a{color: #009AFF;}

    }
</style>
<section id="download">
<div class="container">
<div class="downloadbox">
<p class="titletxt">安全云助手APP下载</p>
<div class="qrcode"><img alt="" src="images/image_18.png"/></div>
<p class="tip">扫码下载 安全云助手APP，支持IOS和Andriod系统。</p>
<div class="icon">
<img alt="" src="images/image_19.png"/>
<img alt="" src="images/image_20.png"/>
</div>
</div>
<!-- <div class="downloadbox">
          <p  class="titletxt">洪恩消防APP下载</p>
          <div class="qrcode"><img src="images/heqrcode.png" alt=""></div>
          <p class="tip">扫码下载 洪恩消防APP，支持IOS和Andriod系统。</p>
          <div class="icon">
            <img src="images/ios.png" alt="">
            <img src="images/andr.png" alt="">
          </div>
        </div> -->
</div>
</section>
<style>
   #download {background: #E92C1E;}
   #download .container {display: flex;justify-content: center;}
   #download .downloadbox {text-align: center;margin:0 20px;}
   #download .downloadbox .titletxt{color: #fff;font-size: 42px;margin-top: 100px;}
   #download .downloadbox .qrcode img{width: 255px;height: 255px;background: #fff;margin:63px auto 34px;padding: 10px; }
   #download .downloadbox .tip{font-size: 14px;color: #fff;}
   #download .downloadbox .icon{margin:28px auto 123px;display: flex;justify-content: center;}
   #download .downloadbox .icon img{width: 36px;height: 43px;margin:0 23px;}
   
   @media screen and (max-width: 768px) {
      #download .downloadbox .titletxt{font-size: 32px;margin-top: 40px;}
      #download .downloadbox .qrcode img{width: 120px;height: 120px;background: #fff;margin:22px auto 26px;padding: 6px; }
      #download .downloadbox .tip{font-size: 10px;}
      #download .downloadbox .icon{margin:19px auto 49px;}
      #download .downloadbox .icon img{width: 21px;height: 20px;margin:0 5px;}
   }

</style>
<!-- Service Section of index Page -->
<footer id="footer">
<div class="container pcbox">
<div class="footercont">
<p class="first"><a href="about_tengyu.html">关于我们</a><a href="success_cases.html">成功案例</a><a href="contact_us.html">合作咨询</a><a href="industry_news.html">行业动态</a></p>
<p class="second">腾御（上海）信息科技有限公司 | 
              <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a> | 
              <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a>
</p>
<p class="third">Copyright©2019TENGYU.,Ltd | 版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
</div>
</div>
<div class="mobbox">
<ul>
<li>
<a href="about_tengyu.html">
<span>关于我们</span>
<img alt="" src="images/image_21.png"/>
</a>
</li>
<li>
<a href="success_cases.html">
<span>成功案例</span>
<img alt="" src="images/image_22.png"/>
</a>
</li>
<li>
<a href="contact_us.html">
<span>合作咨询</span>
<img alt="" src="images/image_23.png"/>
</a>
</li>
<li>
<a href="industry_news.html">
<span>行业动态</span>
<img alt="" src="images/image_24.png"/>
</a>
</li>
</ul>
<div class="footerbottom">
<p class="company">腾御（上海）信息科技有限公司</p>
<p><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a></p>
<p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a></p>
<p>Copyright©2019TENGYU.,Ltd</p>
<p>版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
</div>
</div>
</footer>
</div>
<!-- /End of Footer Section -->
<!--Scroll To Top-->
<a class="scrollup" href="#"><i class="fa fa-chevron-up"></i></a>
<!--/End of Scroll To Top-->
<script id="wp-embed-js" src="js/script_6.js" type="text/javascript"></script>
<style>
    #footer {text-align: center;background: #1E3854;}
    .footercont p {color: #fff;}
    .footercont .first {margin-top: 42px;font-size: 16px;}
    .footercont .first a {margin:0 20px;color: #F0F0F0;}
    .footercont .second {margin-top: 35px;font-size: 10px;}
    .footercont .second a{color: #fff;}
    .footercont .third {margin-top: 5px;font-size: 10px;margin-bottom: 40px;}
    .pcbox {display: block;}
    .mobbox {display: none;}
    @media screen and (max-width: 1100px) {
        .container {width: 100%;}
    }
    @media screen and (max-width: 768px) {
        .container {width: 100%;}
        .pcbox {display: none;}
        .mobbox {display: block;}
        .mobbox ul li a{height: 48px;line-height: 48px;border-bottom:1px solid rgba(255,255,255,.1);margin:0;padding:0;display: flex;justify-content: space-between;align-items: center;padding: 0 24px;}
        .mobbox ul li a span{color: #fff;font-size: 12px;}
        .mobbox ul li img {width: 16px;height:16px;}
        .mobbox .footerbottom {padding:0 24px 27px;}
        .mobbox .footerbottom p {color: #fff;font-size: 12px;text-align: left;}
        .mobbox .footerbottom p.company {line-height: 40px;}
        .mobbox .footerbottom p a{color: #fff;font-size: 12px;}
    }
</style>
<script>
    jQuery(document).ready(function($) {

        $('.i-scrollto').each(function () {
            _this = $(this);

            _this.click(function(){

                var target_offset = _this.offset();
                var target_top = target_offset.top;

                $('html, body').animate({scrollTop:target_top}, 600, 'easeInSine');
            });
        });
    });
    // 如果文章页面body的高度没有整个网页的固定高度高，就让footer部分固定在底部

    // setTimeout(() => {
        if (document.body.offsetHeight<document.documentElement.clientHeight) {
            var footersidebar = document.querySelector('#footer');
            footersidebar.style.position = 'fixed';
            footersidebar.style.bottom = 0;
            footersidebar.style.left = 0;
            footersidebar.style.right = 0;
        }
    // }, 500);
</script>
<link href="css/style_12.css" id="nx-box-shortcodes-css" media="all" rel="stylesheet" type="text/css"/>
</body>
</html>