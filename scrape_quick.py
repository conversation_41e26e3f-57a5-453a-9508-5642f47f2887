#!/usr/bin/env python3
import requests
from bs4 import BeautifulSoup
import os
from urllib.parse import urljoin, urlparse
import time

def scrape_quick():
    """快速抓取关键资源"""
    
    url = "https://www.119.net"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        # 获取原始页面
        print("正在获取页面...")
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = response.apparent_encoding
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 创建输出目录
        output_dir = "119net_quick"
        os.makedirs(output_dir, exist_ok=True)
        
        # 复制已下载的字体文件
        print("复制字体文件...")
        fonts_src = "119net_enhanced/fonts"
        fonts_dst = os.path.join(output_dir, "fonts")
        if os.path.exists(fonts_src):
            os.system(f"cp -r {fonts_src} {fonts_dst}")
            print(f"已复制 {len(os.listdir(fonts_dst))} 个字体文件")
        
        # 复制已下载的图片文件
        print("复制图片文件...")
        images_src = "119net_scraped/images"
        images_dst = os.path.join(output_dir, "images")
        if os.path.exists(images_src):
            os.system(f"cp -r {images_src} {images_dst}")
            print(f"已复制 {len(os.listdir(images_dst))} 个图片文件")
        
        # 复制CSS文件并更新字体路径
        print("处理CSS文件...")
        css_src = "119net_scraped/css"
        css_dst = os.path.join(output_dir, "css")
        os.makedirs(css_dst, exist_ok=True)
        
        if os.path.exists(css_src):
            for css_file in os.listdir(css_src):
                if css_file.endswith('.css'):
                    src_path = os.path.join(css_src, css_file)
                    dst_path = os.path.join(css_dst, css_file)
                    
                    with open(src_path, 'r', encoding='utf-8') as f:
                        css_content = f.read()
                    
                    # 更新字体路径为相对路径
                    css_content = css_content.replace('https://fonts.gstatic.com/', '../fonts/')
                    
                    with open(dst_path, 'w', encoding='utf-8') as f:
                        f.write(css_content)
            
            print(f"已处理 {len(os.listdir(css_dst))} 个CSS文件")
        
        # 复制JS文件
        print("复制JS文件...")
        js_src = "119net_scraped/js"
        js_dst = os.path.join(output_dir, "js")
        if os.path.exists(js_src):
            os.system(f"cp -r {js_src} {js_dst}")
        
        # 更新HTML中的图片引用
        img_tags = soup.find_all('img')
        for i, img in enumerate(img_tags):
            img_src = img.get('src')
            if img_src:
                # 使用通用的图片名称格式
                if 'logo' in img_src.lower():
                    img['src'] = 'images/logo.png'  # 假设第一个是logo
                else:
                    img['src'] = f'images/image_{i+1}.png'
        
        # 保存HTML文件
        html_filename = os.path.join(output_dir, "index.html")
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(str(soup))
        
        print(f"快速版本已保存到: {output_dir}")
        return True
        
    except Exception as e:
        print(f"快速抓取出错: {e}")
        return False

if __name__ == "__main__":
    scrape_quick()