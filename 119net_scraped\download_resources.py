#!/usr/bin/env python3
import asyncio
import os
import re
from urllib.parse import urljoin, urlparse
import aiohttp
import aiofiles
from pathlib import Path

async def download_file(session, url, local_path, base_url="https://www.119.net"):
    """下载文件到本地"""
    try:
        # 处理相对URL
        if url.startswith('//'):
            url = 'https:' + url
        elif url.startswith('/'):
            url = base_url + url
        
        print(f"下载: {url} -> {local_path}")
        
        # 确保目录存在
        Path(local_path).parent.mkdir(parents=True, exist_ok=True)
        
        async with session.get(url) as response:
            if response.status == 200:
                async with aiofiles.open(local_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
                print(f"✓ 成功下载: {local_path}")
                return True
            else:
                print(f"✗ 下载失败 {response.status}: {url}")
                return False
    except Exception as e:
        print(f"✗ 下载错误: {url} - {e}")
        return False

async def process_resources():
    """处理和下载资源"""
    base_dir = "/Users/<USER>/working/tengyu/119.net/119net_scraped"
    
    # 需要下载的资源映射
    resource_map = {
        # WordPress主题资源
        "/wp-content/themes/busiprof/css/bootstrap.css": "css/bootstrap.css",
        "/wp-content/themes/busiprof/css/custom.css": "css/custom.css", 
        "/wp-content/themes/busiprof/css/flexslider.css": "css/flexslider.css",
        "/wp-content/themes/busiprof/css/font-awesome/css/font-awesome.min.css": "css/font-awesome.min.css",
        "/wp-content/themes/busiprof/style.css": "css/style.css",
        "/wp-content/themes/hatatengyu/static/css/box-shortcodes.css": "css/box-shortcodes.css",
        
        # JavaScript文件
        "/wp-content/themes/busiprof/js/jquery.1.9.1.min.js": "js/jquery.1.9.1.min.js",
        "/wp-content/themes/busiprof/js/bootstrap.min.js": "js/bootstrap.min.js",
        "/wp-content/themes/busiprof/js/custom.js": "js/custom.js",
        "/wp-content/themes/busiprof/static/js/jquery.1.8.3.min.js": "js/jquery.1.8.3.min.js",
        
        # 图片资源
        "/wp-content/themes/busiprof/images/brightarrow.png": "images/brightarrow.png",
        "/wp-content/themes/busiprof/images/rightarrow.png": "images/rightarrow.png",
        "/wp-content/themes/busiprof/images/service3.jpg": "images/service3.jpg",
        "/wp-content/themes/busiprof/images/servicebg2.jpg": "images/servicebg2.jpg",
        "/wp-content/themes/busiprof/images/contactbg.jpg": "images/contactbg.jpg",
        "/wp-content/themes/busiprof/images/newsbg.jpg": "images/newsbg.jpg",
        "/wp-content/themes/busiprof/images/support.jpg": "images/support.jpg",
        "/wp-content/themes/busiprof/images/casebgs.jpg": "images/casebgs.jpg",
        "/wp-content/themes/busiprof/images/ouruser.jpg": "images/ouruser.jpg",
        "/wp-content/themes/busiprof/images/servicebg3.jpg": "images/servicebg3.jpg",
        "/wp-content/themes/busiprof/images/aboutbg.jpg": "images/aboutbg.jpg",
        "/wp-content/themes/busiprof/images/access1.jpg": "images/access1.jpg",
        "/wp-content/themes/busiprof/images/andr.png": "images/andr.png",
        "/wp-content/themes/busiprof/images/ios.png": "images/ios.png",
        "/wp-content/themes/busiprof/images/heqrcode.png": "images/heqrcode.png",
        
        # 上传的图片
        "/wp-content/uploads/2019/09/logo1.png": "images/logo1.png",
        "/wp-content/uploads/2020/08/logo.png": "images/logo.png",
        "/wp-content/uploads/2019/10/accessicon1.png": "images/accessicon1.png",
        "/wp-content/uploads/2019/10/accessicon2.png": "images/accessicon2.png",
        "/wp-content/uploads/2019/10/accessicon3.png": "images/accessicon3.png",
        "/wp-content/uploads/2019/10/accessicon4.png": "images/accessicon4.png",
        "/wp-content/uploads/2019/10/accessicon5.png": "images/accessicon5.png",
        "/wp-content/uploads/2022/09/Huolicheng2-1024x560.png": "images/Huolicheng2.png",
        "/wp-content/uploads/2025/03/红利.png": "images/hongli.png",
        "/wp-content/uploads/2025/05/湖南.png": "images/hunan.png",
        "/wp-content/uploads/2025/05/白皮书-1024x535.png": "images/baipishu.png",
        "/wp-content/uploads/2025/06/制作火灾隐患判定规则图片-1024x768.png": "images/huozai_guize.png",
        "/wp-content/uploads/2025/06/图片压缩成功后文件-1024x576.png": "images/yasuo_chenggong.png",
        "/wp-content/uploads/2025/06/火灾报警控制器-728x1024.png": "images/huozai_baojing.png",
        
        # 字体文件
        "/wp-content/static/font/PingFangMedium.ttf": "fonts/PingFangMedium.ttf",
        
        # 其他静态文件
        "/wp-content/themes/busiprof/static/baidumap.html": "static/baidumap.html",
        "/wp-content/themes/busiprof/static/baidumap_mobile.html": "static/baidumap_mobile.html",
    }
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        for remote_path, local_filename in resource_map.items():
            local_path = os.path.join(base_dir, local_filename)
            
            # 跳过已存在的文件
            if os.path.exists(local_path):
                print(f"跳过已存在文件: {local_path}")
                continue
                
            task = download_file(session, remote_path, local_path)
            tasks.append(task)
        
        # 并发下载
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r is True)
        total_count = len(tasks)
        print(f"\n下载完成: {success_count}/{total_count} 个文件成功")

if __name__ == "__main__":
    asyncio.run(process_resources())