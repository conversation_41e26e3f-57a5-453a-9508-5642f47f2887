#!/usr/bin/env python3
"""
创建 productbg.jpg 文件
"""

import os

def create_productbg():
    """创建 productbg.jpg 文件"""
    source = "119net_scraped/images/servicebg2.jpg"
    target = "119net_scraped/images/productbg.jpg"

    try:
        if os.path.exists(source):
            # 使用二进制模式复制文件
            with open(source, 'rb') as src:
                with open(target, 'wb') as dst:
                    dst.write(src.read())
            print(f"✅ 已创建 {target}")
            print(f"📁 源文件: {source}")
            print(f"📁 目标文件: {target}")

            # 验证文件是否创建成功
            if os.path.exists(target):
                size = os.path.getsize(target)
                print(f"📊 文件大小: {size} 字节")
            else:
                print("❌ 文件创建失败")
        else:
            print(f"❌ 源文件不存在: {source}")
    except Exception as e:
        print(f"❌ 创建文件时出错: {e}")

if __name__ == "__main__":
    create_productbg()
