#!/usr/bin/env python3
"""
Git操作助手脚本
帮助管理文件修改和Git操作
"""

import os
import subprocess
import sys
from datetime import datetime

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return -1, "", str(e)

def show_git_status():
    """显示Git状态"""
    print("📊 当前Git状态:")
    print("-" * 40)
    
    returncode, stdout, stderr = run_command("git status --porcelain")
    if returncode != 0:
        print(f"❌ 无法获取Git状态: {stderr}")
        return
    
    if not stdout:
        print("✅ 工作区干净，没有未提交的更改")
        return
    
    modified_files = []
    untracked_files = []
    staged_files = []
    
    for line in stdout.split('\n'):
        if line.strip():
            status = line[:2]
            filename = line[3:]
            
            if status == ' M':
                modified_files.append(filename)
            elif status == '??':
                untracked_files.append(filename)
            elif status == 'M ':
                staged_files.append(filename)
            elif status == 'A ':
                staged_files.append(filename)
    
    if modified_files:
        print("📝 已修改文件 (未暂存):")
        for file in modified_files:
            print(f"  - {file}")
    
    if staged_files:
        print("✅ 已暂存文件:")
        for file in staged_files:
            print(f"  - {file}")
    
    if untracked_files:
        print("❓ 未跟踪文件:")
        for file in untracked_files:
            print(f"  - {file}")

def show_file_diff(filepath):
    """显示文件差异"""
    print(f"🔍 文件差异: {filepath}")
    print("-" * 40)
    
    returncode, stdout, stderr = run_command(f"git diff {filepath}")
    if returncode != 0:
        print(f"❌ 无法获取文件差异: {stderr}")
        return
    
    if not stdout:
        print("✅ 文件没有更改")
        return
    
    print(stdout)

def add_file(filepath):
    """添加文件到暂存区"""
    print(f"📝 添加文件到暂存区: {filepath}")
    
    returncode, stdout, stderr = run_command(f"git add {filepath}")
    if returncode != 0:
        print(f"❌ 添加失败: {stderr}")
        return False
    
    print("✅ 文件已添加到暂存区")
    return True

def commit_changes(message):
    """提交更改"""
    print(f"💾 提交更改: {message}")
    
    # 转义提交消息中的引号
    escaped_message = message.replace('"', '\\"')
    returncode, stdout, stderr = run_command(f'git commit -m "{escaped_message}"')
    
    if returncode != 0:
        print(f"❌ 提交失败: {stderr}")
        return False
    
    print("✅ 更改已提交")
    print(stdout)
    return True

def show_recent_commits():
    """显示最近的提交"""
    print("📚 最近的提交:")
    print("-" * 40)
    
    returncode, stdout, stderr = run_command("git log --oneline -10")
    if returncode != 0:
        print(f"❌ 无法获取提交历史: {stderr}")
        return
    
    print(stdout)

def interactive_menu():
    """交互式菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🔥 Git操作助手")
        print("=" * 50)
        print("1. 查看Git状态")
        print("2. 查看文件差异")
        print("3. 添加文件到暂存区")
        print("4. 提交更改")
        print("5. 查看最近提交")
        print("6. 快速提交index.html")
        print("7. 退出")
        print("-" * 50)
        
        choice = input("请选择操作 (1-7): ").strip()
        
        if choice == "1":
            show_git_status()
        
        elif choice == "2":
            filepath = input("请输入文件路径 (默认: 119net_scraped/index.html): ").strip()
            if not filepath:
                filepath = "119net_scraped/index.html"
            show_file_diff(filepath)
        
        elif choice == "3":
            filepath = input("请输入文件路径 (默认: 119net_scraped/index.html): ").strip()
            if not filepath:
                filepath = "119net_scraped/index.html"
            add_file(filepath)
        
        elif choice == "4":
            message = input("请输入提交消息: ").strip()
            if not message:
                message = f"更新文件 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            commit_changes(message)
        
        elif choice == "5":
            show_recent_commits()
        
        elif choice == "6":
            print("🚀 快速提交index.html文件...")
            if add_file("119net_scraped/index.html"):
                message = f"更新index.html - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                commit_changes(message)
        
        elif choice == "7":
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        command = sys.argv[1]
        
        if command == "status":
            show_git_status()
        elif command == "diff":
            filepath = sys.argv[2] if len(sys.argv) > 2 else "119net_scraped/index.html"
            show_file_diff(filepath)
        elif command == "add":
            filepath = sys.argv[2] if len(sys.argv) > 2 else "119net_scraped/index.html"
            add_file(filepath)
        elif command == "commit":
            message = sys.argv[2] if len(sys.argv) > 2 else f"更新文件 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            commit_changes(message)
        elif command == "quick":
            # 快速提交index.html
            if add_file("119net_scraped/index.html"):
                message = f"更新index.html - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                commit_changes(message)
        else:
            print("❌ 未知命令")
            print("可用命令: status, diff, add, commit, quick")
    else:
        # 交互式模式
        interactive_menu()

if __name__ == "__main__":
    main()
