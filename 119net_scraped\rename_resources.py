#!/usr/bin/env python3
import os
import shutil
from pathlib import Path

def rename_and_copy_resources():
    """重命名和复制下载的资源到正确的位置"""
    base_dir = "/Users/<USER>/working/tengyu/119.net/119net_scraped"
    
    # 将下载的文件重命名并复制到正确的位置
    resource_mappings = [
        # CSS文件 - 复制下载的文件到现有的编号文件
        ("css/bootstrap.css", "css/style_2.css"),
        ("css/custom.css", "css/style_3.css"),
        ("css/flexslider.css", "css/style_4.css"),
        ("css/font-awesome.min.css", "css/style_5.css"),
        ("css/style.css", "css/style_6.css"),
        ("css/box-shortcodes.css", "css/style_12.css"),
        
        # JavaScript文件 - 复制下载的文件到现有的编号文件  
        ("js/jquery.1.9.1.min.js", "js/script_1.js"),
        ("js/bootstrap.min.js", "js/script_2.js"),
        ("js/custom.js", "js/script_3.js"),
        ("js/jquery.1.8.3.min.js", "js/script_5.js"),
    ]
    
    print("开始重命名和复制资源文件...")
    
    for source, target in resource_mappings:
        source_path = os.path.join(base_dir, source)
        target_path = os.path.join(base_dir, target)
        
        if os.path.exists(source_path):
            try:
                # 确保目标目录存在
                Path(target_path).parent.mkdir(parents=True, exist_ok=True)
                
                # 如果目标文件已存在，先备份
                if os.path.exists(target_path):
                    backup_path = target_path + ".backup"
                    shutil.copy2(target_path, backup_path)
                    print(f"✓ 备份了原有文件: {target}")
                
                # 复制文件
                shutil.copy2(source_path, target_path)
                print(f"✓ 复制: {source} -> {target}")
                
            except Exception as e:
                print(f"✗ 复制失败: {source} -> {target}: {e}")
        else:
            print(f"✗ 源文件不存在: {source}")
    
    print("\n资源文件重命名和复制完成!")

if __name__ == "__main__":
    rename_and_copy_resources()