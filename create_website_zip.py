#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建119.net网站的zip包，只包含网站必需文件
"""

import os
import zipfile
import datetime

def create_website_zip():
    """创建网站zip包"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    zip_filename = "119net_website_{}.zip".format(timestamp)
    
    print("开始创建119.net网站包: {}".format(zip_filename))
    print("=" * 50)
    
    included_files = 0
    
    # 要包含的目录和文件
    include_dirs = [
        'css',
        'js', 
        'images',
        'fonts',
        'data',
        'static'
    ]
    
    # 要包含的HTML文件
    html_files = [
        'index.html',
        'about_tengyu.html',
        'building_fire_center.html',
        'contact_us.html',
        'datacenter_battery_whitepaper.html',
        'fire_hazard_rules.html',
        'fire_statistics_rules.html',
        'government_fire_center.html',
        'group_fire_center.html',
        'hunan_fire_detector.html',
        'industry_news.html',
        'job_electrician.html',
        'job_fire_project_manager.html',
        'job_frontend_developer.html',
        'job_nodejs_developer.html',
        'job_sales_manager.html',
        'national_standard_gb4717.html',
        'our_services.html',
        'smart_electrical.html',
        'smart_fire_market_trends.html',
        'success_cases.html',
        'technical_support.html',
        'test_api.html'
    ]
    
    # 其他要包含的文件
    other_files = [
        'API_UPDATE_SUMMARY.md'
    ]
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED, allowZip64=True) as zipf:
        base_dir = '119net_scraped'
        
        # 添加HTML文件
        for html_file in html_files:
            file_path = os.path.join(base_dir, html_file)
            if os.path.exists(file_path):
                arcname = "119net_website/{}".format(html_file)
                zipf.write(file_path, arcname)
                included_files += 1
                print("包含HTML: {}".format(html_file))
        
        # 添加其他文件
        for other_file in other_files:
            file_path = os.path.join(base_dir, other_file)
            if os.path.exists(file_path):
                arcname = "119net_website/{}".format(other_file)
                zipf.write(file_path, arcname)
                included_files += 1
                print("包含文件: {}".format(other_file))
        
        # 添加目录
        for include_dir in include_dirs:
            dir_path = os.path.join(base_dir, include_dir)
            if os.path.exists(dir_path):
                for root, dirs, files in os.walk(dir_path):
                    # 跳过备份文件和Python文件
                    for file in files:
                        if (not file.endswith('.backup') and 
                            not file.endswith('.py') and
                            not file.endswith('.pyc')):
                            
                            file_path = os.path.join(root, file)
                            # 计算相对路径
                            rel_path = os.path.relpath(file_path, base_dir)
                            arcname = "119net_website/{}".format(rel_path.replace('\\', '/'))
                            
                            try:
                                zipf.write(file_path, arcname)
                                included_files += 1
                                print("包含: {}".format(rel_path))
                            except Exception as e:
                                print("跳过: {} (错误: {})".format(rel_path, str(e)))
        
        # 添加根目录的README.md
        if os.path.exists('README.md'):
            zipf.write('README.md', '119net_website/README.md')
            included_files += 1
            print("包含: README.md")
    
    print("=" * 50)
    print("统计信息:")
    print("   包含文件: {}".format(included_files))
    
    if os.path.exists(zip_filename):
        file_size = os.path.getsize(zip_filename) / 1024 / 1024
        print("   压缩包大小: {:.2f} MB".format(file_size))
        print("成功创建: {}".format(zip_filename))
        
        # 创建说明文件
        readme_content = """# 119.net 网站包

## 包信息
- 创建时间: {}
- 包含文件: {} 个
- 压缩包大小: {:.2f} MB
- 版本: {}

## 目录结构
119net_website/
├── index.html              # 首页
├── *.html                  # 各个页面文件
├── css/                    # 样式文件
├── js/                     # JavaScript文件
├── images/                 # 图片资源
├── fonts/                  # 字体文件
├── data/                   # 数据文件
├── static/                 # 静态资源
├── API_UPDATE_SUMMARY.md   # API更新说明
└── README.md               # 项目说明

## 部署说明
1. 解压到Web服务器目录
2. 确保服务器支持静态文件服务
3. 访问 index.html 即可

## 技术特性
- 响应式设计
- 动态统计数据（调用生产API）
- 完整的页面导航
- 优化的资源加载

## 重要说明
- 首页统计数据已更新为调用生产环境API
- API地址: https://prod-sub-api.119.net/admin/statistics/getHomePageInfo
- 包含完整的错误处理和回退机制

## 联系信息
如有问题，请联系技术支持。

---
生成时间: {}
""".format(
            datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S"),
            included_files,
            file_size,
            timestamp,
            datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        
        readme_filename = "119net_website_{}_README.txt".format(timestamp)
        with open(readme_filename, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("已创建说明文件: {}".format(readme_filename))
        print("打包完成！")
        
        return zip_filename
    else:
        print("创建zip文件失败！")
        return None

if __name__ == "__main__":
    create_website_zip()
