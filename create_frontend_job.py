#!/usr/bin/env python3
"""
创建前端开发工程师页面
"""

# 读取NodeJS页面作为模板
with open("119net_scraped/job_nodejs_developer.html", 'r', encoding='utf-8') as f:
    template = f.read()

# 替换内容创建前端开发工程师页面
frontend_content = template.replace(
    "NodeJS软件开发工程师", "web前端开发工程师"
).replace(
    "postid-335", "postid-408"
).replace(
    "job_nodejs_developer.html", "job_frontend_developer.html"
).replace(
    "2020 年 08 月 05 日", "2020 年 08 月 06 日"
).replace(
    """<h4>岗位职责：</h4>
<p>– 基于NodeJS的快速开发平台的设计与实现。<br>
– 物联网业务平台后端逻辑和接口实现。<br>
– 分布式数据存储，海量数据分析，（日增长千万条数据）</p>

<h4>岗位要求：</h4>
<p>– 3年以上NodeJS后端开发经验；<br>
– 有成熟的Koa或者Express项目经验。<br>
– 熟练掌握mongodb，mysql, redis等常用数据库；<br>
– 熟悉JavaScript事件机制，熟悉ES6特性；<br>
– 熟悉MVC开发模式;<br>
– 有分布式项目经验优先。</p>""",
    """<h4>岗位职责：</h4>
<p>– 负责数据中心、管理平台、移动Wap、小程序的前段设计与开发；<br>
– 数据可视化的设计与实现；<br>
– 参与相关页面的Web&Wap前端架构设计、核心代码的编写；<br>
– 进行详细设计、代码开发, 配合测试, 高质量完成项目；<br>
– 按照项目计划，按时提交高质量代码，完成开发任务；</p>

<h4>岗位要求：</h4>
<p>– 熟悉主流前端框架，如vue、react、angular中的1~2种；<br>
– 熟悉RequireJS、webpack，且现有项目中有所实践；<br>
– 有大型项目框架设计实施经验；<br>
– 有全栈发展意愿优先。公司将提供你的学习和提升空间；<br>
– 具有良好的沟通能力和团队合作精神。</p>"""
)

# 写入文件
with open("119net_scraped/job_frontend_developer.html", 'w', encoding='utf-8') as f:
    f.write(frontend_content)

print("✅ 前端开发工程师页面创建完成！")
