#!/usr/bin/env python3
"""
创建完美的政府消防数据中心页面
参照原始页面 https://www.119.net/?page_id=320
"""

import os

def create_government_page():
    """创建政府消防数据中心页面"""
    
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>	
	<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
	
<title>政府数据中心 – 九消消防物联网</title>
<meta name='robots' content='max-image-preview:large' />

<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; Feed" href="index.html" />
<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; 评论Feed" href="index.html" />

<link rel='stylesheet' id='wp-block-library-css'  href='css/style_1.css' type='text/css' media='all' />
<link rel='stylesheet' id='busiprof-style-css'  href='css/style.css' type='text/css' media='all' />
<link rel='stylesheet' id='bootstrap-css-css'  href='css/bootstrap.css' type='text/css' media='all' />
<link rel='stylesheet' id='busiporf-custom-css-css'  href='css/custom.css' type='text/css' media='all' />
<link rel='stylesheet' id='flexslider-css-css'  href='css/flexslider.css' type='text/css' media='all' />
<link rel='stylesheet' id='font-awesome-css-css'  href='css/font-awesome.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='box-shortcodes-css'  href='css/box-shortcodes.css' type='text/css' media='all' />

<script type='text/javascript' src='js/script_1.js' id='jquery-js'></script>
<script type='text/javascript' src='js/script_2.js' id='bootstrap-js-js'></script>
<script type='text/javascript' src='js/script_3.js' id='busiporf-custom-js-js'></script>
<script type='text/javascript' src='js/script_6.js' id='flexslider-js'></script>

<link rel="icon" href="images/logo1.png" sizes="32x32" />
<link rel="icon" href="images/logo1.png" sizes="192x192" />
<link rel="apple-touch-icon" href="images/logo1.png" />
<meta name="msapplication-TileImage" content="images/logo1.png" />

<style type="text/css">
.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}
img.wp-smiley, img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
.site-title, .site-description {
	position: absolute;
	clip: rect(1px, 1px, 1px, 1px);
}
</style>
	
</head>
<body class="page-template page-template-template-govdata page-template-template-govdata-php page page-id-320 wp-custom-logo">
	
<div id="page" class="site">

<!-- Navbar -->	
<nav class="navbar navbar-default">
	<div class="maincontainer">
		<!-- Brand and toggle get grouped for better mobile display -->
		<div class="navbar-header">
			<span class="navbar-brand">
				<a href="index.html" class="custom-logo-link" rel="home">
					<img width="91" height="42" src="images/logo.png" class="custom-logo" alt="九消消防物联网" />
				</a>
			</span>			
			<div class="custom-logo-link-url">
		    	<h1 class="site-title">
		    		<a class="navbar-brand" href="index.html">九消消防物联网</a>
		    	</h1>
		    	<p class="site-description">智慧消防 &#8211; 腾御科技</p>
			</div>
				
			<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1">
				<span class="sr-only">Toggle navigation</span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
			</button>
		</div>

		<!-- Collect the nav links, forms, and other content for toggling -->
		<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
			<ul id="menu-menu" class="nav navbar-nav navbar-right">
				<li id="menu-item-288" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-288">
					<a href="index.html">首页<img class="arrow" src="images/brightarrow.png"/></a>
				</li>
				<li id="menu-item-34" class="menu-item menu-item-type-custom menu-item-object-custom current-menu-ancestor current-menu-parent menu-item-has-children menu-item-34 dropdown active">
					<a>产品与服务<img class="arrow" src="images/brightarrow.png"/><span class="caret"></span></a>
					<ul class="dropdown-menu">
						<li id="menu-item-328" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-328">
							<a href="building_fire_center.html">楼宇消防接入中心<img class="arrow" src="images/brightarrow.png"/></a>
						</li>
						<li id="menu-item-329" class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-320 current_page_item menu-item-329 active">
							<a href="government_fire_center.html">政府消防数据中心<img class="arrow" src="images/brightarrow.png"/></a>
						</li>
						<li id="menu-item-331" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-331">
							<a href="group_fire_center.html">集团消防数据中心<img class="arrow" src="images/brightarrow.png"/></a>
						</li>
						<li id="menu-item-332" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-332">
							<a href="smart_electrical.html">智慧用电<img class="arrow" src="images/brightarrow.png"/></a>
						</li>
						<li id="menu-item-333" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-333">
							<a href="our_services.html">我们的服务<img class="arrow" src="images/brightarrow.png"/></a>
						</li>
						<li id="menu-item-334" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-334">
							<a target="_blank" rel="noopener" href="https://book.yunzhan365.com/bookcase/ukwe/index.html">产品资料<img class="arrow" src="images/brightarrow.png"/></a>
						</li>
					</ul>
				</li>
				<li id="menu-item-35" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-35">
					<a href="success_cases.html">成功案例<img class="arrow" src="images/brightarrow.png"/></a>
				</li>
				<li id="menu-item-36" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-36">
					<a href="industry_news.html">行业动态<img class="arrow" src="images/brightarrow.png"/></a>
				</li>
				<li id="menu-item-37" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-37">
					<a href="about_tengyu.html">关于腾御<img class="arrow" src="images/brightarrow.png"/></a>
				</li>
				<li id="menu-item-38" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-38">
					<a href="technical_support.html">技术支持<img class="arrow" src="images/brightarrow.png"/></a>
				</li>
				<li id="menu-item-39" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-39">
					<a href="contact_us.html">联系我们<img class="arrow" src="images/brightarrow.png"/></a>
				</li>
			</ul>
		</div><!-- /.navbar-collapse -->
	</div><!-- /.container-fluid -->
</nav>

<!-- Main Content -->
<main id="content" class="site-content">
	<section class="government-data-center">
		<!-- Hero Banner -->
		<div class="hero-banner">
			<div class="hero-content">
				<h1 class="hero-title">政府消防数据中心</h1>
				<p class="hero-subtitle">消防主管部门用于督查监管</p>
			</div>
		</div>
		
		<!-- Platform Introduction -->
		<div class="platform-section">
			<div class="container">
				<div class="platform-content">
					<div class="platform-image">
						<img src="images/service3.jpg" alt="政府消防数据中心" />
					</div>
					<div class="platform-text">
						<h2 class="section-title">平台介绍</h2>
						<div class="description">
							<p>涵盖消防应急管理全部工作流程和管辖对象。全国首套消防应急管理整体解决方案。可以根据管理部门实际需求分拆、组合、定制各子系统，实现点菜式定制消防应急管理接入中心。有法律依据，有数据标准，有执行细节的政府管理台。通过消防物联网远程传输设备，将消防设备运行数据动态实时上传到"政府数据中心"建筑消防隐患一目了然。</p>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<!-- Features Section -->
		<div class="features-section">
			<div class="container">
				<div class="features-grid">
					<div class="feature-item">
						<div class="feature-indicator"></div>
						<div class="feature-text">
							<p>辖区内消防基础数据收集。九个管理对象：建筑、单位、消防水源、维保、街道等</p>
						</div>
					</div>
					
					<div class="feature-item">
						<div class="feature-indicator"></div>
						<div class="feature-text">
							<p>辖区内消防运维自动督查 火警核实、设备巡查维修监测等</p>
						</div>
					</div>
					
					<div class="feature-item">
						<div class="feature-indicator"></div>
						<div class="feature-text">
							<p>政府需要的数据的输出现场执法、火场作战支持</p>
						</div>
					</div>
					
					<div class="feature-item">
						<div class="feature-indicator"></div>
						<div class="feature-text">
							<p>明确的物联监测标准 支持21套消防系统，278中硬件物联监测，8项业务流程监管，全部有法律依据和数据标准</p>
						</div>
					</div>
					
					<div class="feature-item">
						<div class="feature-indicator"></div>
						<div class="feature-text">
							<p>针对重点单位、九小场所、老旧小区、市政消火栓等场景都有不同的解决方案。</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
</main>

<style>
/* 政府消防数据中心页面样式 */
.government-data-center {
	background: #fff;
	overflow: hidden;
}

/* Hero Banner */
.hero-banner {
	background: url(images/servicebg2.jpg) no-repeat center center;
	background-size: cover;
	width: 100%;
	height: 580px;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.hero-banner::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
}

.hero-content {
	text-align: center;
	position: relative;
	z-index: 2;
}

.hero-title {
	font-size: 72px;
	color: #fff;
	letter-spacing: 18px;
	margin: 0;
	font-weight: bold;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
	font-size: 24px;
	color: #fff;
	margin: 48px 0 0 0;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Platform Section */
.platform-section {
	padding: 80px 0;
	background: #fff;
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.platform-content {
	display: flex;
	align-items: center;
	gap: 60px;
}

.platform-image {
	flex: 1;
}

.platform-image img {
	width: 100%;
	height: auto;
	border-radius: 12px;
	box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.platform-text {
	flex: 1;
}

.section-title {
	font-size: 48px;
	font-weight: 700;
	color: #000;
	margin-bottom: 36px;
	line-height: 1.2;
}

.description p {
	color: #2D2E30;
	line-height: 1.8;
	font-size: 18px;
	text-align: justify;
	margin: 0;
}

/* Features Section */
.features-section {
	background: #f8f9fa;
	padding: 80px 0;
}

.features-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 40px;
	max-width: 800px;
	margin: 0 auto;
}

.feature-item {
	display: flex;
	align-items: flex-start;
	gap: 30px;
	background: #fff;
	padding: 30px;
	border-radius: 12px;
	box-shadow: 0 4px 20px rgba(0,0,0,0.08);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.feature-indicator {
	width: 8px;
	height: 8px;
	background: linear-gradient(135deg, #0469AB, #0084C9);
	border-radius: 50%;
	margin-top: 8px;
	flex-shrink: 0;
}

.feature-text p {
	color: #333;
	font-size: 16px;
	line-height: 1.6;
	margin: 0;
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
	.container {
		padding: 0 30px;
	}
	
	.platform-content {
		gap: 40px;
	}
	
	.hero-title {
		font-size: 60px;
		letter-spacing: 12px;
	}
}

@media screen and (max-width: 768px) {
	.hero-banner {
		height: 400px;
	}
	
	.hero-title {
		font-size: 36px;
		letter-spacing: 6px;
	}
	
	.hero-subtitle {
		font-size: 18px;
		margin-top: 24px;
	}
	
	.platform-section {
		padding: 60px 0;
	}
	
	.platform-content {
		flex-direction: column;
		gap: 40px;
	}
	
	.section-title {
		font-size: 32px;
		text-align: center;
		margin-bottom: 24px;
	}
	
	.description p {
		font-size: 16px;
	}
	
	.features-section {
		padding: 60px 0;
	}
	
	.feature-item {
		padding: 25px 20px;
		gap: 20px;
	}
	
	.feature-text p {
		font-size: 15px;
	}
}

@media screen and (max-width: 480px) {
	.container {
		padding: 0 20px;
	}
	
	.hero-title {
		font-size: 28px;
		letter-spacing: 3px;
	}
	
	.hero-subtitle {
		font-size: 16px;
	}
	
	.section-title {
		font-size: 28px;
	}
}
</style>'''
    
    # 添加页脚
    footer_content = '''
<footer id="footer">
	<div class="container pcbox">		
        <div class="footercont">
            <div class="footerlinks">
                <ul>
                    <li><a href="about_tengyu.html">关于我们 <img src="images/rightarrow.png" alt=""></a></li>
                    <li><a href="success_cases.html">成功案例 <img src="images/rightarrow.png" alt=""></a></li>
                    <li><a href="contact_us.html">合作咨询 <img src="images/rightarrow.png" alt=""></a></li>
                    <li><a href="industry_news.html">行业动态 <img src="images/rightarrow.png" alt=""></a></li>
                </ul>
            </div>
            <div class="footertext">
                <p>腾御（上海）信息科技有限公司</p>
                <p><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a></p>
                <p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a></p>
                <p>Copyright©2019TENGYU.,Ltd</p>
                <p>版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
            </div>
        </div>
    </div>
    
    <div class="container mobilebox">
        <div class="footercont">
            <div class="footerlinks">
                <ul>
                    <li><a href="about_tengyu.html">关于我们 <img src="images/rightarrow.png" alt=""></a></li>
                    <li><a href="success_cases.html">成功案例 <img src="images/rightarrow.png" alt=""></a></li>
                    <li><a href="contact_us.html">合作咨询 <img src="images/rightarrow.png" alt=""></a></li>
                    <li><a href="industry_news.html">行业动态 <img src="images/rightarrow.png" alt=""></a></li>
                </ul>
            </div>
            <div class="footertext">
                <p>腾御（上海）信息科技有限公司</p>
                <p><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a></p>
                <p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a></p>
                <p>Copyright©2019TENGYU.,Ltd</p>
                <p>版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
            </div>
        </div>
    </div>
</footer>

</div><!-- #page -->

</body>
</html>'''
    
    # 合并完整内容
    full_content = html_content + footer_content
    
    # 写入文件
    with open('119net_scraped/government_fire_center.html', 'w', encoding='utf-8') as f:
        f.write(full_content)
    
    print("✅ 政府消防数据中心页面已创建完成")
    print("📄 文件: 119net_scraped/government_fire_center.html")
    print("🌐 访问地址: http://localhost:5000/government-fire")

if __name__ == "__main__":
    create_government_page()
