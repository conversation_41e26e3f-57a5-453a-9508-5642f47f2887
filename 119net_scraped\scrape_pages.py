#!/usr/bin/env python3
import requests
import time
import os

# 主要页面列表
main_pages = [
    {'id': '317', 'name': 'building_fire_center'},        # 楼宇消防接入中心
    {'id': '320', 'name': 'government_fire_center'},      # 政府消防数据中心
    {'id': '324', 'name': 'group_fire_center'},           # 集团消防数据中心
    {'id': '322', 'name': 'smart_electrical'},            # 智慧用电
    {'id': '326', 'name': 'our_services'},                # 我们的服务
    {'id': '67', 'name': 'success_cases'},                # 成功案例
    {'id': '61', 'name': 'industry_news'},                # 行业动态
    {'id': '63', 'name': 'about_tengyu'},                 # 关于腾御
    {'id': '59', 'name': 'technical_support'},            # 技术支持
    {'id': '65', 'name': 'contact_us'},                   # 联系我们
]

# 新闻详情页列表
news_pages = [
    {'id': '871', 'name': 'fire_hazard_rules'},           # 重大火灾隐患判定规则
    {'id': '868', 'name': 'fire_statistics_rules'},       # 火灾统计管理规定
    {'id': '865', 'name': 'national_standard_gb4717'},    # 国家标准GB 4717-2024
]

def scrape_page(page_id, page_name, is_post=False):
    """爬取指定页面"""
    if is_post:
        url = f"https://www.119.net/?p={page_id}"
    else:
        url = f"https://www.119.net/?page_id={page_id}"
    
    print(f"正在爬取: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get(url, headers=headers, timeout=30)
        response.encoding = 'utf-8'
        
        if response.status_code == 200:
            # 保存页面内容
            filename = f"{page_name}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"✓ 已保存: {filename}")
            return True
        else:
            print(f"✗ 失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始爬取119.net页面...")
    
    success_count = 0
    total_count = len(main_pages) + len(news_pages)
    
    # 爬取主要页面
    print("\n=== 爬取主要页面 ===")
    for page in main_pages:
        if scrape_page(page['id'], page['name']):
            success_count += 1
        time.sleep(2)  # 避免请求过频
    
    # 爬取新闻页面
    print("\n=== 爬取新闻页面 ===")
    for page in news_pages:
        if scrape_page(page['id'], page['name'], is_post=True):
            success_count += 1
        time.sleep(2)  # 避免请求过频
    
    print(f"\n=== 爬取完成 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"失败: {total_count - success_count}")

if __name__ == "__main__":
    main()