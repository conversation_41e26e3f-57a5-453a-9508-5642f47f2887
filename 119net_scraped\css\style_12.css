/*** TemplatesNext Shortcode - box elements ***/

/*		Common styles
---------------------------------------------------------------*/

.nx-clearfix:before,
.nx-clearfix:after {
	display: table;
	content: " ";
}
.nx-clearfix:after { clear: both; }

/*		Tabs + Tab
---------------------------------------------------------------*/

.nx-tabs {
	margin: 0 0 1.5em 0;
	padding: 3px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	background: #eee;
}
.nx-tabs-nav span {
	display: inline-block;
	margin-right: 3px;
	padding: 10px 15px;
	min-height: 40px;
	line-height: 20px;
	-webkit-border-top-left-radius: 3px;
	-moz-border-radius-topleft: 3px;
	border-top-left-radius: 3px;
	-webkit-border-top-right-radius: 3px;
	-moz-border-radius-topright: 3px;
	border-top-right-radius: 3px;
	color: #333;
	cursor: pointer;
	-webkit-transition: all .2s;
	-moz-transition: all .2s;
	-o-transition: all .2s;
	transition: all .2s;
}
.nx-tabs-nav span:hover { background: #f5f5f5; }
.nx-tabs-nav span.nx-tabs-current { background: #fff; cursor: default; }
.nx-tabs-nav span.nx-tabs-disabled {
	opacity: 0.5;
	filter: alpha(opacity=50);
	cursor: default;
}
.nx-tabs-pane {
	padding: 15px;
	-webkit-border-bottom-right-radius: 3px;
	-moz-border-radius-bottomright: 3px;
	border-bottom-right-radius: 3px;
	-webkit-border-bottom-left-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	border-bottom-left-radius: 3px;
	background: #fff;
	color: #333;
}
.nx-tabs-vertical:before,
.nx-tabs-vertical:after {
	content: " ";
	display: table;
}
.nx-tabs-vertical:after { clear: both; }
.nx-tabs-vertical .nx-tabs-nav {
	float: left;
	width: 30%;
}
.nx-tabs-vertical .nx-tabs-nav span {
	display: block;
	margin-right: 0;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	-webkit-border-top-left-radius: 3px;
	-moz-border-radius-topleft: 3px;
	border-top-left-radius: 3px;
	-webkit-border-bottom-left-radius: 3px;
	-moz-border-radius-bottomleft: 3px;
	border-bottom-left-radius: 3px;
}
.nx-tabs-vertical .nx-tabs-panes {
	float: left;
	width: 70%;
}
.nx-tabs-vertical .nx-tabs-pane {
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	-webkit-border-top-right-radius: 3px;
	-webkit-border-bottom-right-radius: 3px;
	-moz-border-radius-topright: 3px;
	-moz-border-radius-bottomright: 3px;
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
}
.nx-tabs-nav,
.nx-tabs-nav span,
.nx-tabs-panes,
.nx-tabs-pane {
	-webkit-box-sizing: border-box !important;
	-moz-box-sizing: border-box !important;
	box-sizing: border-box !important;
}

/*		Spoiler + Accordion
---------------------------------------------------------------*/

.nx-spoiler { margin-bottom: 1.5em; }
.nx-spoiler .nx-spoiler:last-child { margin-bottom: 0; }
.nx-accordion { margin-bottom: 1.5em; }
.nx-accordion .nx-spoiler { margin-bottom: 0.5em; }
.nx-spoiler-title {
	position: relative;
	cursor: pointer;
	min-height: 20px;
	line-height: 20px;
	padding: 12px 12px 12px 40px;
	font-weight: bold;
}
.nx-spoiler-icon {
	position: absolute;
	left: 16px;
	top: 12px;
	display: block;
	width: 20px;
	height: 20px;
	line-height: 21px;
	text-align: center;
	font-size: 14px;
	font-family: FontAwesome;
	font-weight: normal;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	*margin-right: .3em;
}
.nx-spoiler-content {
	padding: 16px;
	-webkit-transition: padding-top .2s;
	-moz-transition: padding-top .2s;
	-o-transition: padding-top .2s;
	transition: padding-top .2s;
	-ie-transition: padding-top .2s;
}
.nx-spoiler.nx-spoiler-closed > .nx-spoiler-content {
	height: 0;
	margin: 0;
	padding: 0;
	overflow: hidden;
	border: none;
	opacity: 0;
}
.nx-spoiler-icon-plus .nx-spoiler-icon:before { content: "\f068"; }
.nx-spoiler-icon-plus.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f067"; }
.nx-spoiler-icon-plus-circle .nx-spoiler-icon:before { content: "\f056"; }
.nx-spoiler-icon-plus-circle.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f055"; }
.nx-spoiler-icon-plus-square-1 .nx-spoiler-icon:before { content: "\f146"; }
.nx-spoiler-icon-plus-square-1.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f0fe"; }
.nx-spoiler-icon-plus-square-2 .nx-spoiler-icon:before { content: "\f117"; }
.nx-spoiler-icon-plus-square-2.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f116"; }
.nx-spoiler-icon-arrow .nx-spoiler-icon:before { content: "\f063"; }
.nx-spoiler-icon-arrow.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f061"; }
.nx-spoiler-icon-arrow-circle-1 .nx-spoiler-icon:before { content: "\f0ab"; }
.nx-spoiler-icon-arrow-circle-1.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f0a9"; }
.nx-spoiler-icon-arrow-circle-2 .nx-spoiler-icon:before { content: "\f01a"; }
.nx-spoiler-icon-arrow-circle-2.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f18e"; }
.nx-spoiler-icon-chevron .nx-spoiler-icon:before { content: "\f078"; }
.nx-spoiler-icon-chevron.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f054"; }
.nx-spoiler-icon-chevron-circle .nx-spoiler-icon:before { content: "\f13a"; }
.nx-spoiler-icon-chevron-circle.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f138"; }
.nx-spoiler-icon-caret .nx-spoiler-icon:before { content: "\f0d7"; }
.nx-spoiler-icon-caret.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f0da"; }
.nx-spoiler-icon-caret-square .nx-spoiler-icon:before { content: "\f150"; }
.nx-spoiler-icon-caret-square.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f152"; }
.nx-spoiler-icon-folder-1 .nx-spoiler-icon:before { content: "\f07c"; }
.nx-spoiler-icon-folder-1.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f07b"; }
.nx-spoiler-icon-folder-2 .nx-spoiler-icon:before { content: "\f115"; }
.nx-spoiler-icon-folder-2.nx-spoiler-closed .nx-spoiler-icon:before { content: "\f114"; }
.nx-spoiler-style-default { }
.nx-spoiler-style-default > .nx-spoiler-title {
	padding-left: 40px;
	padding-right: 0;
}
.nx-spoiler-style-default > .nx-spoiler-title > .nx-spoiler-icon { left: 16px; }
.nx-spoiler-style-default > .nx-spoiler-content { padding: 16px 0 16px 16px; }
.nx-spoiler-style-fancy {
	background: #fff;
	color: #333;
}
.nx-spoiler-style-fancy > .nx-spoiler-title {
	background: transparent;
	border: 1px solid #e7e7e7;
}
.nx-spoiler-style-fancy.nx-spoiler-closed > .nx-spoiler-title {}
.nx-spoiler-style-fancy > .nx-spoiler-content {
}
.nx-spoiler-style-simple {
}
.nx-spoiler-style-simple > .nx-spoiler-title {
	padding: 12px 16px;
	background: #f0f0f0;
	color: #333;
}
.nx-spoiler-style-simple > .nx-spoiler-title > .nx-spoiler-icon { display: none; }
.nx-spoiler-style-simple > .nx-spoiler-content {
	padding: 1em 10px;
	background: #fff;
	color: #333;
}

/*		Quote
---------------------------------------------------------------*/

.nx-quote-style-default {
	position: relative;
	margin-bottom: 1.5em;
	padding: 0.5em 3em;
	font-style: italic;
}
.nx-quote-style-default.nx-quote-has-cite { margin-bottom: 3em; }
.nx-quote-style-default:before,
.nx-quote-style-default:after {
	position: absolute;
	display: block;
	width: 20px;
	height: 20px;
	background-image: url('../images/quote.png');
	content: '';
}
.nx-quote-style-default:before {
	top: 0;
	left: 0;
	background-position: 0 0;
}
.nx-quote-style-default:after {
	right: 0;
	bottom: 0;
	background-position: -20px 0;
}
.nx-quote-style-default .nx-quote-cite {
	position: absolute;
	right: 4em;
	bottom: -1.5em;
	font-style: normal;
}
.nx-quote-style-default .nx-quote-cite:before { content: "\2014\0000a0"; }
.nx-quote-style-default .nx-quote-cite a { text-decoration: underline; }

/*		Pullquote
---------------------------------------------------------------*/

.nx-pullquote {
	display: block;
	width: 30%;
	padding: 0.5em 1em;
}
.nx-pullquote-align-left {
	margin: 0.5em 1.5em 1em 0;
	padding-left: 0;
	float: left;
	border-right: 5px solid #eee;
}
.nx-pullquote-align-right {
	margin: 0.5em 0 1em 1.5em;
	padding-right: 0;
	float: right;
	border-left: 5px solid #eee;
}

/*		Row + Column
---------------------------------------------------------------*/

.nx-row {
	/* clear: both; */
	/* zoom: 1; */
	/* margin-bottom: 24px; */
}
.nx-row:before,
.nx-row:after {
	display: table;
	content: "";
}
.nx-row:after {/* clear: both; */}
.nx-column {
	display: block;
	margin: 0 4% 0 0;
	float: left;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.nx-column-last { margin-right: 0; }
.nx-row .nx-column { margin: 0 0 0 4%; }
.nx-row .nx-column .nx-column { margin: 0px!important; display: inline-block; width: 49%; }
.nx-row .nx-column.nx-column-size-1-1 { margin-left: 0; margin-right: 0; }
.nx-row .nx-column:first-child { margin-left: 0; }
.nx-column-centered {
	margin-right: auto !important;
	margin-left: auto !important;
	float: none !important;
}
.nx-column img,
.nx-column iframe,
.nx-column object,
.nx-column embed { max-width: 100%; }
@media only screen {
	[class*="nx-column"] + [class*="nx-column"]:last-child { float: right; }
}
.nx-column-size-1-1 { width: 100%; }
.nx-column-size-1-2 { width: 48%; }


.nx-column-size-1-3 { width: 30.66%; }
.nx-column-size-2-3 { width: 65.33%; }
.nx-column-size-1-4 { width: 22%; }
.nx-column-size-3-4 { width: 74%; }
.nx-column-size-1-5 { width: 16.8%; }
.nx-column-size-2-5 { width: 37.6%; }
.nx-column-size-3-5 { width: 58.4%; }
.nx-column-size-4-5 { width: 79.2%; }
.nx-column-size-1-6 { width: 13.33%; }
.nx-column-size-5-6 { width: 82.66%; }


/* Styles for screens that are less than 768px */
@media only screen and (max-width: 768px) {
	.nx-column {
		width: 100% !important;
		margin: 0 0 24px 0 !important;
		float: none !important;
	}
	
	.nx-row .nx-column:last-child,
	.fancy-inner .nx-column:last-child {
		margin-bottom: 0 !important;
	}
}

.site-content .nx-row.lined div.nx-column {
	border: 1px solid #999;
}


/*		Service
---------------------------------------------------------------*/

.nx-service {
	position: relative;
	margin: 0 0 1.5em 0;
}
.nx-service-title {
	display: block;
	margin-bottom: 20px;
	color: #333;
	font-weight: 400;
	font-size: 20px;
}
.nx-service-title img {
	position: absolute;
	top: 0;
	left: 0;
	display: block !important;
	margin: 0 !important;
	padding: 0 !important;
	border: none !important;
	-webkit-box-shadow: none !important;
	-moz-box-shadow: none !important;
	box-shadow: none !important;
}
.nx-service-title i {
	position: absolute;
	top: 4px;
	left: 0;
	display: block !important;
	width: 1em;
	height: 1em;
	text-align: center;
	line-height: 1em;
}
.nx-service-content {}


.nx-service-title .service-icon-wrap.round i {
	position: absolute;
	top: 4px;
	left: 0;
	display: block !important;
	width: 2em;
	height: 2em;
	padding-top: .5em;
	text-align: center;
	line-height: 1em;
	background-color: #0F0;
	color: #FFFFFF;
	border-radius: 50%;
}

.nx-service-title .service-icon-wrap.square i {
	position: absolute;
	top: 4px;
	left: 0;
	display: block !important;
	width: 2em;
	height: 2em;
	padding-top: .5em;
	text-align: center;
	line-height: 1em;
	background-color: #0F0;
	color: #FFFFFF;
}

.nx-service-title .service-icon-wrap.curved i {
	position: absolute;
	top: 4px;
	left: 0;
	display: block !important;
	width: 2em;
	height: 2em;
	padding-top: .5em;
	text-align: center;
	line-height: 1em;
	background-color: #0F0;
	color: #FFFFFF;
	border-radius: 3px;
}

/*		Box
---------------------------------------------------------------*/

.nx-box {
	margin: 0 0 1.5em 0;
	border-width: 1px;
	border-style: solid;
}
.nx-box-title {
	display: block;
	padding: 12px 16px;
	font-size: 15px;
	font-weight: bold;
}
.nx-box-content {
	background-color: #fff;
	color: #444;
	padding: 16px;
}
.nx-box-style-soft .nx-box-title {
	background-image: url('../images/style-soft.png');
	background-position: 0 0;
	background-repeat: repeat-x;
}
.nx-box-style-glass .nx-box-title {
	background-image: url('../images/style-glass.png');
	background-position: 0 50%;
	background-repeat: repeat-x;
}
.nx-box-style-bubbles .nx-box-title {
	background-image: url('../images/style-bubbles.png');
	background-position: 0 50%;
	background-repeat: repeat-x;
}
.nx-box-style-noise .nx-box-title {
	background-image: url('../images/style-noise.png');
	background-position: 0 0;
	background-repeat: repeat-x;
}

/*		Note
---------------------------------------------------------------*/

.nx-note {
	margin: 0 0 1.5em 0;
	border-width: 1px;
	border-style: solid;
}
.nx-note-inner {
	padding: 1em;
	border-width: 1px;
	border-style: solid;
}

/*		Common margin resets for box elements
---------------------------------------------------------------*/

.nx-column-inner > *:first-child,
.nx-accordion > *:first-child,
.nx-spoiler-content > *:first-child,
.nx-service-content > *:first-child,
.nx-box-content > *:first-child,
.nx-note-inner > *:first-child { margin-top: 0; }
.nx-column-inner > *:last-child,
.nx-tabs-pane > *:last-child,
.nx-accordion > *:last-child,
.nx-spoiler-content > *:last-child,
.nx-service-content > *:last-child,
.nx-box-content > *:last-child,
.nx-note-inner > *:last-child { margin-bottom: 0; }



.nx-arrow {
	display: none;
}

/*
.row.fancy-block {
	border: 1px solid #9C0;
}

.row.fancy-block.arrow-down {
	padding-bottom: 30px;
}

.row.fancy-block.arrow-up {
	padding-top: 30px;
}
*/

.arrow-up,
.arrow-down {
	position: relative;
}

.arrow-down .nx-arrow {
	display: block;
	width: 0; 
	height: 0; 
	border-left: 30px solid transparent;
	border-right: 30px solid transparent;
	border-top: 30px solid #2f2f2f;
	font-size: 0;
	line-height: 0;
	position: absolute;
	left: 50%;
	top: 100%;
	margin-left: -30px;
}

.fancy-block-contents .fancy-inner {
	margin-left: auto;
	margin-right: auto;
	max-width: 1264px;
	padding-left: 32px;
	padding-right: 32px;
	position: relative;
}

.arrow-down .fancy-block-contents {
	margin-bottom: 30px;
}


.arrow-up .nx-arrow {
	display: block;
	width: 0; 
	height: 0; 
	border-left: 30px solid transparent;  /* left arrow slant */
	border-right: 30px solid transparent; /* right arrow slant */
	border-bottom: 30px solid #2f2f2f; /* bottom, add background color here */
	font-size: 0;
	line-height: 0;
	position: absolute;
	left: 50%;
	bottom: 100%;
	margin-left: -30px;	
}

.arrow-up .fancy-block-contents {
	margin-top: 30px;
}

/**
 * 5.0 iconbox
 * ----------------------------------------------------------------------------
 */

.iconbox {
	display: block;
	text-align: center;
	padding: 0px 12px;
	position: relative;
}

.nx-hide-overflow {
	overflow: hidden;
	width: auto;
	position: relative;
}

.iconbox .icon-wrap {
	margin: 0px auto;
	cursor: default;
}

.iconbox .icon-wrap i {
	color: #77be32;
	font-weight: 300;
	margin: 16px 16px 16px 16px;
	border-radius: 50%;
	text-align: center;
	position: relative;
	z-index: 1;
	box-shadow: 0 0 0 2px #77be32;
	-webkit-transition: color 0.4s;
	-moz-transition: color 0.4s;
	transition: color 0.4s;	
}

.iconbox .iconbox-content-wrap h3 {
	margin: 12px 0px;
	font-weight: 400;
	font-size: 20px;
}

.iconbox:hover .iconbox-content-wrap h3 {
	color: #77be32;	
}

.iconbox .iconbox-content-wrap .iconbox-content {
	padding: 16px 0px;	
}

.icon-wrap i:after {/**/
	pointer-events: none;
	position: absolute;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	content: '';
	-webkit-box-sizing: content-box; 
	-moz-box-sizing: content-box; 
	box-sizing: content-box;
}

.icon-wrap i:before {
	speak: none;
	text-transform: none;
	display: block;
	-webkit-font-smoothing: antialiased;
}


/* Effect 3 */
.icon-wrap i:after {
	top: -2px;
	left: -2px;
	padding: 2px;
	z-index: -1;
	background: #77be32;
	-webkit-transition: -webkit-transform 0.2s, opacity 0.3s;
	-moz-transition: -moz-transform 0.2s, opacity 0.3s;
	transition: transform 0.2s, opacity 0.3s;
}

/* Effect 3b */

.nx-icobox .icon-wrap {
	overfloww: hidden;
}

.icon-wrap i {
	color: #77be32;
}

.no-touch .iconbox:hover .icon-wrap i {
	color: #FFFFFF;
}


.icon-wrap i:after {
/**/
	-webkit-transform: scale(1.3);
	-moz-transform: scale(1.3);
	-ms-transform: scale(1.3);
	transform: scale(1.3);	

	opacity: 0;
	clear: both;
}
/**/
.no-touch .iconbox:hover .icon-wrap i:after {
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
	-ms-transform: scale(1);
	transform: scale(1);
	opacity: 1;
}

ul.standard-arrow,
ul.standard-arrow li {
	margin: 0px;
	padding: 0px;
	list-style-type: none;
}

ul.standard-arrow li {
	line-height: 32px;
	padding-left: 0px;
}

ul.standard-arrow li i {
	padding-right: 16px;
	color: #77be32;
}

.ibox-left .icon-wrap {
	padding: 0px 2px; 
}


/**
 * 4.0 iconbox default
 * ----------------------------------------------------------------------------
 */

.nx-iconbox {
	position: relative;
	margin-bottom: 12px;
	margin-top: 12px;
}
.nx-iconbox .nx-iconbox-title {
	padding-left: 52px;
	min-height: 32px;
	line-height: 32px;
	font-weight: 400;
	font-size: 20px;
	margin: 24px 0px;		
}
  
.nx-iconbox .nx-iconbox-title i {
	position: absolute;
	top: 6px;
	left: 0px;
	display: block;
	width: 32px;
	height: 32px;
	text-align: center;
	line-height: 32px;
	font-size: 32px;
	color: #77bd32;
	color: #666;
}

.nx-iconbox .nx-iconbox-content {
	padding-left: 52px;	
}

/* left styled icon box */

.iconbox.ibox-left {
	position: relative;
	text-align: left;
}

.iconbox.ibox-left .icon-wrap i {
	margin-top: 0px;
}

.iconbox.ibox-left .iconbox-content-wrap h3 {
	margin: 0px;
	line-height: 1;
}

.iconbox.ibox-left .icon-wrap {
	display: inline-block;
	position: absolute;
	left: 0px;
	top: 6px;
}

/* iconbox top curved */

.iconbox2.ibox-topcurved {
	text-align: center;
}

.iconbox2.ibox-topcurved .icon-wrap i {
	margin: 16px 16px 16px 16px;
	border-radius: 12px;
	border: 6px solid #e7e7e7;
	text-align: center;
	color: #FFFFFF;
	transition-property: all;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
	transition-delay: 0.1s;		
}

.iconbox2.ibox-topcurved.ibox-topsquare .icon-wrap i {
	border-radius: 0px;
}



/**
 * 8.1 Parallax
 * ----------------------------------------------------------------------------
 */ 


.video-parallax {
	display: block;
	position: relative;
	z-index: 301;
	overflow: hidden;
} 

.fullwidthrow {
	position: relative;
	margin: 0px;
	padding: 0px;
}

.boxed .fullwidthrow {
	margin: 0px -32px;
	width: 1264px;
}

.video-parallax .prallax-wrap {
	position: relative;
	z-index: 333;
	padding: 0px;
	margin: 0px auto;
}

.video-parallax .prallax-wrap .parallax-contents {
	display: block;
	padding: 72px 0px;
	text-align: center;
	color: #fff;
}

.video-parallax .video-overlay {
	display: block;
	position: absolute;
	left: 0px;
	top: 0px;
	bottom: 0px;
	width: 100%;
	background-color: rgba(0,0,0,0.0);
	z-index: 321;
	transition-property: all;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
	transition-delay: 0.2s;		
}

.video-parallax.parainview .video-overlay {
	background-color: rgba(0,0,0,0.6);
}

.video-parallax .video-wrap {
	position: absolute;
	left: 0px;
	top: 0px;
	min-width: 100%;
}

.video-parallax video {
	position: relative;
	min-width: 100%;
}

.fullwidthinner {
	position: absolute;
	overflow: hidden;
	z-index: 101;
}


/**
 * 8.2 Image parallax
 * ----------------------------------------------------------------------------
 */

.image-parallax {
	display: block;
	position: relative;
	z-index: 301;
	overflow: hidden;
} 

.image-parallax .prallax-wrap,
.nx-fixedbg-parallax .prallax-wrap-fixed {
	position: relative;
	z-index: 333;
	padding: 0px;
	margin: 0px auto;
}

.image-parallax .prallax-wrap .parallax-contents,
.nx-fixedbg-parallax .prallax-wrap-fixed .parallax-contents {
	display: block;
	padding: 48px 0px;
	text-align: center;
	color: #fff;
	clear: both;
}

.image-parallax .image-overlay {
	display: block;
	position: absolute;
	left: 0px;
	top: 0px;
	bottom: 0px;
	width: 100%;
	background-color: rgba(0,0,0,0.0);
	z-index: 321;
	transition-property: all;
	transition-duration: 0.5s;
	transition-timing-function: ease-in-out;
	transition-delay: 0.2s;		
}

.image-parallax.parainview .image-overlay {
	background-color: rgba(0,0,0,0.6);
}

.image-parallax .image-wrap {
	position: absolute;
	left: 0px;
	top: 0px;
	min-width: 100%;
	min-height: 100%;
}

.image-parallax .image-wrap img {
	position: relative;
	min-width: 100%;
}


.video-parallax .prallax-wrap {
	max-width: 1264px;
	padding: 0px 32px;
}

/* .image-parallax .prallax-wrap {
	max-width: 1264px;
	padding: 0px 32px;
} */

.nx-fixedbg-parallax .prallax-wrap-fixed .parallax-contents {
	max-width: 1264px;
	margin: 0px auto;
	text-align: center;
	color: #FFF;
}

.calltoact-contents {
	max-width: 1264px;
	padding: 32px 32px;
}

.site-content .image-parallax .image-wrap img {
	width: auto;
	min-width: 100%;	
	max-width: 9999px;
}

.site-content .video-parallax .video-wrap video {
	width: auto;
	max-width: 9999px;
}


@media (max-width: 1199px) {
}



/*		Row + Column
---------------------------------------------------------------*/

.nx-column {
	display: block;
	margin: 0 0 0 0;
	padding: 0px 16px;
}
.nx-column-last { padding-right: 0px; }
.nx-row .nx-column { margin: 0%; }
.nx-row .nx-column.nx-column-size-1-1 { padding-left: 0; padding-right: 0; }
.nx-row .nx-column:first-child { padding-left: 0px; }
.nx-row .nx-column:last-child { padding-right: 0px; }

.nx-column-size-1-1 { width: 100%; }
.nx-column-size-1-2 { width: 50%; }
.nx-column-size-1-3 { width: 33.33%; }
.nx-column-size-2-3 { width: 66.66%; }
.nx-column-size-1-4 { width: 25%; }
.nx-column-size-3-4 { width: 75%; }
.nx-column-size-1-5 { width: 20%; }
.nx-column-size-2-5 { width: 40%; }
.nx-column-size-3-5 { width: 60%; }
.nx-column-size-4-5 { width: 80%; }
.nx-column-size-1-6 { width: 16.66%; }
.nx-column-size-5-6 { width: 83.33%; }

.nx-col-1-2 {
	width: 50%;
	display: block;
	float: left;
	min-height: 1px;	
}
.nx-col-1-3 {
	width: 33.3%;
	display: block;
	float: left;
	min-height: 1px;		
}
.nx-col-2-3 {
	width: 66.66%;
	display: block;
	float: left;
	min-height: 1px;		
}
.nx-col-1-4 {
	width: 25%;
	display: block;
	float: left;
	min-height: 1px;		
}
.nx-col-1-5 {
	width: 20%;
	display: block;
	float: left;
	min-height: 1px;		
}
.nx-col-1-6 {
	width: 16.666%;
	display: block;
	float: left;
	min-height: 1px;		
}
.nx-col-3-4 {
	width: 75%;
	display: block;
	float: left;
	min-height: 1px;		
}


/* Styles for screens that are less than 768px */
@media only screen and (max-width: 768px) {
	.nx-column {
		width: 100% !important;
		margin: 0 0 24px 0 !important;
		padding: 0px!important;
		float: none !important;
	}
	
	.nx-row .nx-column:last-child,
	.fancy-inner .nx-column:last-child {
		margin-bottom: 0 !important;
	}
}


/* Absolute Container */

.nx-abs-container-wrap {
	display: block;
	position: relative;
	width: 100%;
	height: 0px;
}

.nx-abdcontainer {
	display: block;
	position: absolute;
	height: auto;
	z-index: 1012;
}

.nx-absblock-title {
	display: block;
	padding: 6px 16px;
}

.nx-abs-inner {
	display: block;
	clear: both;
}

.nx-abs-inner .wpcf7-form textarea {
	height: 96px;
}

.nx-abs-inner .wpcf7 input[type="text"], 
.nx-abs-inner .wpcf7 input[type="tel"], 
.nx-abs-inner .wpcf7 input[type="password"], 
.nx-abs-inner .wpcf7 input[type="url"], 
.nx-abs-inner .wpcf7 input[type="email"], 
.nx-abs-inner .wpcf7 textarea {
	padding-top: 10px;
	padding-bottom: 10px;
	border: 1px solid #c7c7c7;
}

.nx-abs-inner .wpcf7 p {
	margin-bottom: 6px;
}


@media only screen and (max-width: 1264px) {
	.boxed .fullwidthrow {
		width: auto;
	}
}

@media only screen and (max-width: 999px) {
	.nx-abs-container-wrap {
		height: auto;
		padding-bottom: 16px;
	}
	.iconbox.ibox-left .iconbox-content-wrap h3 {
		margin-top: 16px;
	}
}

.nx-row.nx-white-bordered,
.nx-row.nx-row-nomargin {
	margin-bottom: 0px;
}
.nx-row.nx-white-bordered .nx-col-1-2, 
.nx-row.nx-white-bordered .nx-col-1-3,
.nx-row.nx-white-bordered .nx-col-2-3,
.nx-row.nx-white-bordered .nx-col-1-4,
.nx-row.nx-white-bordered .nx-col-3-4 {
	border-right: 0px solid #FFF;
}

.nx-row.nx-white-bordered .nx-col-1-2:last-child, 
.nx-row.nx-white-bordered .nx-col-1-3:last-child,
.nx-row.nx-white-bordered .nx-col-2-3:last-child,
.nx-row.nx-white-bordered .nx-col-1-4:last-child,
.nx-row.nx-white-bordered .nx-col-3-4:last-child {
	border-right: 0px solid #FFF;
}

.nx-service-teaser {
	text-align: center;
	position: relative;
	margin: 0px;
	padding: 0px 1px 1px 0px;
}

.nx-service-teaser:first-child {
	padding: 0px 0px 1px 1px;
}

.nx-service-teaser .nx-service-teaser-icon {
	display: block;
	background-color: #474747;
	color: #dd9933;
	text-align: center;
	padding: 0px;
	margin: 0px auto;
	transition-property: all;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
	transition-delay: 0s;	
}

.nx-service-teaser:hover .nx-service-teaser-icon {
	background-color: #dd9933;
	color: #FFFFFF;
}

.nx-service-teaser-icon i {
	font-size: 64px;
	line-height: 120px;
	color: #dd9933;
	transition-property: all;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
	transition-delay: 0.1s;	
}

.nx-service-teaser:hover .nx-service-teaser-icon i {
	color: #FFFFFF;
}


.nx-service-teaser .nx-service-teaser-content h2 {
	font-size: 24px;
	margin: 0px 0px 24px 0px;
	transition-property: all;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
	transition-delay: 0s;	
}

.nx-service-teaser-content {
	background-color: #FFFFFF;
	padding: 16px 32px 32px 32px;
	margin-top: 6px;
	transition-property: all;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
	transition-delay: 0s;		
}

.nx-service-teaser:hover .nx-service-teaser-content {
	color: #FFFFFF;
	background-color: #474747;
	margin-top: 0px;
	padding-top: 22px;
}

.nx-service-teaser:hover .nx-service-teaser-content h2 {
	color: #FFFFFF;
}

.nx-service-teaser .nx-service-teaser-content a {
	color: #dd9933;
}
.nx-service-teaser .nx-service-teaser-content a:hover {
	color: #ffffff;
}


.nx-cube {
	width: 100%;
	text-align: center;
	margin: 0 auto;
	height: 120px;

	-webkit-transition: -webkit-transform .33s;
	transition: transform .33s; /* Animate the transform properties */

	-webkit-transform-style: preserve-3d;
	transform-style: preserve-3d; /* <-NB */
}

/* The two faces of the cube */
.nx-flippety,.nx-flop {
	height: 120px;
	text-align: center;
}

.nx-flippety i,.nx-flop i {
	font-size: 64px;
	line-height: 120px;
	color: #dd9933;
	transition-property: all;
	transition-duration: 0.4s;
	transition-timing-function: ease-in-out;
	transition-delay: 0.1s;	
}

/* Position the faces */
.nx-flippety {
	-webkit-transform: translateZ(50px);
	transform: translateZ(50px);
	background-color: #474747;
	color: #dd9933;
}

.nx-flop {
	-webkit-transform: rotateX(-90deg) translateZ(-50px);
	transform: rotateX(-90deg) translateZ(-50px);
	background-color: #dd9933;
	color: #FFFFFF!important;
}

.nx-flop i {
	color: #FFFFFF!important;
}

.nx-flop i {
	color: #FFFFFF;
}

/* Rotate the cube */
.nx-service-teaser:hover .nx-cube {
	-webkit-transform: rotateX(90deg);
	transform: rotateX(90deg); /* Text bleed at 90Âº */
}

/* slide Down Menu Box */

.nx-prodmenu-col {
	padding-right: 1px;
	padding-top: 1px;
	padding-bottom: 1px;
}

.nx-prodmenu-col:first-child {
	padding-left: 1px;
}


.nx-slide-down-box a{
    text-decoration:none;
    outline:none;
}

.nx-slide-down-box {
    float: left;
    width: 170px;
	width: 100%;
    height: 85px;
    position: relative;
    cursor: pointer;
}

.nx-slide-down-box > a {
    position:absolute;
    top:0px;
    left:0px;
    width:170px;
	width: 100%;
    height:85px;
    z-index: 1212;
	background-color: #575757;
}

.nx-slide-down-box a img {
    border:none;
    position:absolute;
    width:0px;
    height:0px;
    bottom:0px;
    left:50%;
    z-index: 1230;
}

.nx-slide-down-box span.sdt_wrap{
    position:absolute;
    top:0px;
    left:0px;
    width:170px;
	width: 100%;
    height:60px;
    z-index: 1215;
}

.nx-slide-down-box span.sdt_active{
    position:absolute;
    background:#111;
    top:85px;
    width:170px;
	width: 100%;
	height: 100%;
    height:0px;
    left:0px;
    z-index: 1214;
	/*
    -moz-box-shadow:0px 0px 4px #000 inset;
    -webkit-box-shadow:0px 0px 4px #000 inset;
    box-shadow:0px 0px 4px #000 inset;
	*/
}

.nx-slide-down-box span span.sdt_link,
.nx-slide-down-box span span.sdt_descr,
.nx-slide-down-box div.sdt_box a{
    margin-left: 16px;
    margin-right: 16px;	
    text-transform:uppercase;
}

.nx-slide-down-box span span.sdt_link{
    color:#fff;
    font-size:24px;
    float:left;
    clear:both;
	margin-top: 20px;
}
.nx-slide-down-box span span.sdt_descr{
    color: #CCC;
    float:left;
    clear:both;
    width:90%;
    font-size:10px;
    letter-spacing:1px;
}

.nx-slide-down-box .sdt_box{
    display:block;
    position:absolute;
    width:100%;

    overflow:hidden;
    height:170px;
    top:85px;
    left:0px;
    display:none;
    background:#000;
	z-index: 1117;
	padding: 16px;
	color: #CCC;
}
.nx-slide-down-box div.sdt_box a {
    float:left;
    clear:both;
    line-height: 30px;
    color:#dd3333;
	margin-left: 0px;
	margin-right: 0px;
}

.nx-slide-down-box div.sdt_box a:first-child{
}
.nx-slide-down-box div.sdt_box a:hover{
    color:#fff;
}

.fullwidthrow.overflow-yes {
	z-index: 1112;
	overflow: visible!important;
}

.fullwidthrow.overflow-yes > div {
	overflow: visible!important;
}

.no-padding .fancy-block-contents .fancy-inner {
	padding: 0px;
}


/*
* vertical product categories
*
*/

.nx-vertical-menu .vmenu-title {
	display: block;
	padding: 12px 16px;
	font-size: 14px;
	line-height: 16px;
	background-color: #dd3333;
	color: #FFF;
	font-weight: bold;
	border-bottom: 1px solid #e7e7e7;	
	
}

.nx-vertical-menu.dark .vmenu-title {
	border-bottom: 1px solid #959595;	
	border-top: 1px solid #959595;	
}

.nx-vertical-menu ul,
.nx-vertical-menu .menu li {
	margin: 0px;
	padding: 0px;
	list-style-type: none;
}

.nx-vertical-menu .menu li {
	display: block;
	position: relative;
	border-top: 1px solid #e7e7e7;	
}

.nx-vertical-menu .menu li:first-child {
	border-top: 0px solid #e7e7e7;	
}

.nx-vertical-menu > .menu li.menu-item-has-children > a:after {
	position: absolute;
	right: 12px;
	top: 40%;
	content: "\f054";
	display: inline-block;
	font-family: FontAwesome;
	font-style: normal;
	font-weight: normal;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-size: 10px;
	color: #999;
	z-index: 1006;
}
	
.nx-vertical-menu .menu li.menu-item-has-children > a:hover:after {
	color: #FFF;
}

.nx-vertical-menu .menu li a {
	display: block;
	line-height: 16px;
	padding: 12px 16px;
	background-color: #ffffff;
}

.nx-vertical-menu .menu li a:hover {
	color: #FFF;
	background-color: #757575;
}

.nx-vertical-menu ul > li ul {
	display: none;
	position: absolute;
	z-index: 1005;
	left: 100%;
	margin-right: -240px;
	top: 0px;
	width: 240px;
	background-color: #FFF;
	border-bottom-width: 2px;	
}

.nx-vertical-menu ul li:hover > ul {
	display: block;
	visibility: visible;
	margin-right: -260px;
}

.nx-vertical-menu > ul li:hover > ul {
	animation-name: nx-fadeInUp;	
}

.nx-vertical-menu > .menu > li.nx-icon > a {
	padding-left: 40px;
}
/**/
.nx-vertical-menu > ul > li.nx-icon:before {
	font-size: 18px;
	line-height: 18px;
	position: absolute;
	z-index: 109;
	left: 16px;
	top: 9px;
	font-family: 'FontAwesome';
	display: inline-block;
	font-family: FontAwesome;
	font-style: normal;
	font-weight: 300;
	line-height: 1;
	color: #878787;
}

.nx-vertical-menu > ul > li.nx-icon:hover:before {
	color: #FFF;
}



/* dark version */

.nx-vertical-menu.dark .menu li {
	border-top: 1px solid #959595;	
}

.nx-vertical-menu.dark .menu li:first-child {
	border-top: 0px solid #959595;	
}

.nx-vertical-menu.dark .menu li.menu-item-has-children a:after {
	color: #FFF;
}
	
.nx-vertical-menu.dark .menu li.menu-item-has-children a:hover:after {
	color: #757575;
}

.nx-vertical-menu.dark .menu li a {
	background-color: #757575;
	color: #FFF;
}

.nx-vertical-menu.dark .menu li a:hover {
	color: #757575;
	background-color: #FFFFFF;
}

.nx-vertical-menu.dark ul > li > ul {
	background-color: #FFF;
}
@media (min-width: 768px) {
	.nx-vertical-menu ul > li > ul.nx-mega {
		width: 780px;
		height: auto;
	}
	
	.nx-vertical-menu ul > li > ul.nx-mega > li {
		width: 780px;
		height: auto;
		border: 1px solid #0F0;
	}
	
	.nx-vertical-menu ul > li > ul.nx-mega > li > ul {
		display: block;
		float: none;
		visibility: visible;
		position: relative;
		left: auto;
		top: auto;
		box-shadow: none;
		width: 720px;
		border: 1px solid #0F0;
	}
	
	.nx-vertical-menu ul > li > ul.nx-mega > li > ul > li {
		display: block;
		float: left;
		border: 1px solid #999;
		width: 240px;
	}
	
	.nx-vertical-menu ul > li > ul.nx-mega > li > ul > li a {
		display: block;
		float: none;
		border: 0px solid #999;
		padding: 0px;
		line-height: 20px;
	}
	
	.nx-vertical-menu ul > li > ul.nx-mega > li > ul ul {
		display: block;
		visibility: visible;
		position: relative;
		left: auto;
		top: auto;
		box-shadow: none;
		width: 100%;
		height: auto;
		transition-duration: 0.0s;
	}
	
	.nx-vertical-menu ul > li > ul.nx-mega > li > ul > li li {
		display: block;
		float: none;
		width: 100%;
	}
	
	.nx-vertical-menu ul > li > ul.nx-mega > li > ul > li {
	}
}


@media (max-width: 1069px) {
	.nx-col-1-6 {
		width: 33.333%;
		display: block;
		float: left;
		min-height: 1px;		
	}

	.nx-slide-down-box .sdt_box{
		display: block;
		height: 85px;
		top: auto;
		bottom: 0px;
		left:0px;
		z-index: 1212;
		opacity: 0;		
		transition-property: all;
		transition-duration: 0.4s;
		transition-timing-function: ease-in-out;
		transition-delay: 0.1s;			
	}
	
	.nx-slide-down-box div.sdt_box a {
		float:left;
		clear:none;
		padding-right: 16px;
	}
		
	.nx-slide-down-box:hover .sdt_box{
		display:block;
		bottom: 85px;
		height: auto;
		min-height: 85px;
		opacity: 1;		
	}
	.nx-prodmenu-col:first-child {
		padding-left: 0px;
	}	
		
}

@media (min-width: 768px) {
	
	.nx-vertical-menu ul > li ul {
		-webkit-box-shadow: 0px 4px 2px 0px rgba(50, 50, 50, 0.2);
		-moz-box-shadow:    0px 4px 2px 0px rgba(50, 50, 50, 0.2);
		box-shadow:         0px 4px 2px 0px rgba(50, 50, 50, 0.2);
		animation-duration: 0.5s;
		animation-fill-mode: both;
		animation-timing-function: ease-in-out;
	}
	/**/
}

@media (max-width: 767px) {

	.nx-col-3-4,
	.nx-col-2-3,
	.nx-col-1-6,
	.nx-col-1-5,
	.nx-col-1-4,
	.nx-col-1-3,
	.nx-col-1-2 {
		width: 100%;
		display: block;
		float: none;
	}
	.nx-prodmenu-col {
		padding-right: 0px;
		clear: both;
	}
	
	.nx-vertical-menu ul > li > ul {
		display: block;
		visibility: visible;
		position: relative;
		height: auto;
		left: auto;
		margin-right: 0px;
		top: auto;
		width: 100%;
		-webkit-box-shadow: none;
		-moz-box-shadow:    none;
		box-shadow:         none;	
		border: 0px solid #FFF;
		border-bottom: 0px solid #FFF!important;			
	}
	
	.nx-vertical-menu .menu li.menu-item-has-children > a:after {
		position: absolute;
		left: 95%;
		top: 14px;
		content: "\f078";
	}

	.nx-vertical-menu .menu li:first-child {
		border-top: 1px solid #e7e7e7;	
	}

	.nx-vertical-menu menu.ul > li > a {
	}		
	.nx-vertical-menu ul.menu > li > ul > li > a {
		padding-left: 32px;
	}		
	.nx-vertical-menu ul.menu > li > ul > li > ul > li > a {
		padding-left: 48px;
	}		
	.nx-vertical-menu ul.menu > li > ul > li > ul > li > ul > li > a {
		padding-left: 64px;
	}
	
	.nx-vertical-menu.dark .menu > li > ul li:first-child {
		border-top: 1px solid #959595;	
	}	
		
}
