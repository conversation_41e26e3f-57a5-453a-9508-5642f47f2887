<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
    <style type="text/css">
        body, html {width: 100%;height: 100%;margin:0;font-family:"微软雅黑";overflow: hidden;}
        #allmap{width:100%;height:1528px;}
        p{margin-left:5px; font-size:14px;}
    </style>
    <title>百度地图</title>
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=2.0&ak=LmKi9dK3iwasAjGlRiMIAnNqDsRZ9duk&s=1"></script>
</head>
<body>
<div id="allmap"></div>
<!--<p>点击标注点，可查看由文本，图片构成的复杂型信息窗口</p>-->
</body>
</html>
<script type="text/javascript">
    // 百度地图API功能
    var sContent =
        // "<h4 style='margin:0 0 5px 0;padding:0.2em 0'>腾御（上海）信息科技有限公司</h4>" +
        // "<img style='float:right;margin:4px' id='imgDemo' src='../img/tianAnMen.jpg' width='139' height='104' title='天安门'/>" +
        "<p style='margin:0;line-height:1.5;font-size:13px;text-indent:2em;font-family: '微软雅黑';'>上海市 杨浦区 鞍山路5号</p>" +
        "<p style='margin:0;line-height:1.5;font-size:13px;text-indent:2em;font-family: '微软雅黑';'>杨浦商城18楼</p>" +
        "<p style='margin:0;line-height:1.5;font-size:13px;text-indent:2em;font-family: '微软雅黑';'>021-66690267</p>" +
        "</div>";
    var map = new BMap.Map("allmap");
    // var point = new BMap.Point(121.521449, 31.279486);
    var point = new BMap.Point(121.519619, 31.280174);
    // var marker = new BMap.Marker(point);
    var myIcon = new BMap.Icon("./images/position.png", new BMap.Size(60, 120), {  
        imageSize: new BMap.Size(60, 120)
        // offset: new BMap.Size(100, 170), // 指定定位位置  
        // imageOffset: new BMap.Size(0, -170) // 设置图片偏移  
    });  
    var marker=new BMap.Marker(point,{icon:myIcon});  

    const label = new BMap.Label(
      '上海市 杨浦区 鞍山路5号<br/>杨浦商城18楼<br/>4006659119<br/>业务部13761916848（同微信）',
      { offset: new BMap.Size( -160, 120 ) }
    )
    label.setStyle({
        // color: "#000",
        color: "#000",
        fontSize: "20px",
        width: '340px',
        height: "168px",
        lineHeight: "36px",
        fontFamily:"Microsoft YaHei",
        border:0,
        background: '#fff',
        paddingLeft: '34px',
        paddingTop: '25px',
    });
    marker.setLabel(label);

    // var infoWindow = new BMap.InfoWindow(sContent);  // 创建信息窗口对象
    map.centerAndZoom(point, 20);
    map.panBy(0,-300)
    map.addOverlay(marker);
    // marker.addEventListener("click", function(){
    //     console.log(this)
    //     this.openInfoWindow(infoWindow);
    //     //图片加载完毕重绘infowindow
    //     document.getElementById('imgDemo').onload = function (){
    //         infoWindow.redraw();   //防止在网速较慢，图片未加载时，生成的信息框高度比图片的总高度小，导致图片部分被隐藏
    //     }
    // });
    // marker.openInfoWindow(infoWindow, 100);
</script>
