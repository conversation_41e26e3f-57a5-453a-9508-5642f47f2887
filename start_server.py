#!/usr/bin/env python3
"""
119.net 服务器启动脚本
提供多种启动选项
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_requirements():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查静态文件目录
    static_dir = "119net_scraped"
    if not os.path.exists(static_dir):
        print(f"❌ 错误: 静态文件目录 '{static_dir}' 不存在")
        print("请先运行网站抓取脚本")
        return False
    
    print(f"✅ 静态文件目录: {static_dir}")
    
    # 检查关键文件
    key_files = ['index.html', 'css', 'js', 'images']
    missing_files = []
    for file in key_files:
        if not os.path.exists(os.path.join(static_dir, file)):
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️  警告: 缺少文件/目录: {', '.join(missing_files)}")
    else:
        print("✅ 关键文件检查通过")
    
    return True

def install_flask():
    """安装Flask依赖"""
    print("📦 安装Flask...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "flask"])
        print("✅ Flask安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ Flask安装失败")
        return False

def check_flask():
    """检查Flask是否已安装"""
    try:
        import flask
        print(f"✅ Flask已安装 (版本: {flask.__version__})")
        return True
    except ImportError:
        print("❌ Flask未安装")
        return False

def start_simple_server(port=8000):
    """启动Python内置HTTP服务器"""
    print(f"🚀 启动Python内置HTTP服务器 (端口: {port})...")
    print(f"🌐 访问地址: http://localhost:{port}")
    print("按 Ctrl+C 停止服务器\n")
    
    os.chdir("119net_scraped")
    try:
        subprocess.run([sys.executable, "-m", "http.server", str(port)])
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

def start_flask_server(port=5000, debug=True):
    """启动Flask服务器"""
    print(f"🚀 启动Flask服务器 (端口: {port})...")
    print(f"🌐 访问地址:")
    print(f"   主页: http://localhost:{port}")
    print(f"   管理面板: http://localhost:{port}/admin")
    print(f"   API: http://localhost:{port}/api/pages")
    print("按 Ctrl+C 停止服务器\n")
    
    # 设置环境变量
    env = os.environ.copy()
    env['FLASK_APP'] = 'app.py'
    if debug:
        env['FLASK_ENV'] = 'development'
        env['FLASK_DEBUG'] = '1'
    
    try:
        subprocess.run([sys.executable, "app.py"], env=env)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="119.net 服务器启动脚本")
    parser.add_argument("--type", choices=["simple", "flask"], default="flask",
                       help="服务器类型 (默认: flask)")
    parser.add_argument("--port", type=int, default=None,
                       help="端口号 (simple默认8000, flask默认5000)")
    parser.add_argument("--no-debug", action="store_true",
                       help="禁用Flask调试模式")
    parser.add_argument("--install-deps", action="store_true",
                       help="自动安装依赖")
    
    args = parser.parse_args()
    
    print("🔥 119.net 服务器启动脚本")
    print("=" * 50)
    
    # 检查运行环境
    if not check_requirements():
        sys.exit(1)
    
    # 根据服务器类型设置默认端口
    if args.port is None:
        args.port = 8000 if args.type == "simple" else 5000
    
    if args.type == "flask":
        # 检查Flask
        if not check_flask():
            if args.install_deps:
                if not install_flask():
                    print("请手动安装Flask: pip install flask")
                    sys.exit(1)
            else:
                print("请安装Flask: pip install flask")
                print("或使用 --install-deps 参数自动安装")
                sys.exit(1)
        
        start_flask_server(args.port, not args.no_debug)
    else:
        start_simple_server(args.port)

if __name__ == "__main__":
    main()
