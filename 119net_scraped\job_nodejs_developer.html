<!DOCTYPE html>
<html lang="zh-CN">
<head>	
	<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
	
<title>NodeJS软件开发工程师 &#8211; 九消消防物联网</title>
<meta name='robots' content='max-image-preview:large' />

<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; Feed" href="index.html" />
<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; 评论Feed" href="index.html" />
		<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.7.2"}};
		</script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
	<link rel='stylesheet' id='wp-block-library-css'  href='css/style_1.css' type='text/css' media='all' />

<link rel='stylesheet' id='busiprof-style-css'  href='css/style.css' type='text/css' media='all' />
<link rel='stylesheet' id='bootstrap-css-css'  href='css/bootstrap.css' type='text/css' media='all' />
<link rel='stylesheet' id='busiporf-custom-css-css'  href='css/custom.css' type='text/css' media='all' />
<link rel='stylesheet' id='flexslider-css-css'  href='css/flexslider.css' type='text/css' media='all' />

<link rel='stylesheet' id='font-awesome-css-css'  href='css/font-awesome.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='lazyprof-parent-style-css'  href='css/style.css' type='text/css' media='all' />
<script type='text/javascript' src='js/script_1.js' id='jquery-js'></script>
<script type='text/javascript' src='js/script_2.js' id='bootstrap-js-js'></script>
<script type='text/javascript' src='js/script_3.js' id='busiporf-custom-js-js'></script>
<script type='text/javascript' src='https:' id='xiaofang-js'></script>
<link rel="" href="" />
 
<meta name="generator" content="WordPress 5.7.2" />
<link rel="canonical" href="job_nodejs_developer.html" />
<link rel='shortlink' href='job_nodejs_developer.html' />
		<!-- Custom Logo: hide header text -->
		<style id="custom-logo-css" type="text/css">
			.site-title, .site-description {
				position: absolute;
				clip: rect(1px, 1px, 1px, 1px);
			}
		</style>
		

<style type="text/css">.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}</style><link rel="icon" href="images/logo1.png" sizes="32x32" />
<link rel="icon" href="images/logo1.png" sizes="192x192" />
<link rel="apple-touch-icon" href="images/logo1.png" />
<meta name="msapplication-TileImage" content="images/logo1.png" />
	
</head>
<body class="post-template-default single single-post postid-335 single-format-standard wp-custom-logo">
	
<div id="page" class="site">
	<!-- <a class="skip-link busiprof-screen-reader" href="#content">Skip to content</a> -->
<!-- Navbar -->	
<nav class="navbar navbar-default">
	<!-- <div class="container"> -->
	<div class="maincontainer">
		<!-- Brand and toggle get grouped for better mobile display -->
		<div class="navbar-header">
			<span class="navbar-brand"><a href="index.html" class="custom-logo-link" rel="home"><img width="91" height="42" src="images/logo.png" class="custom-logo" alt="九消消防物联网" /></a></span>			<div class="custom-logo-link-url">
	    	<h1 class="site-title"><a class="navbar-brand" href="index.html" >九消消防物联网</a>
	    	</h1>
	    							<p class="site-description">智慧消防 &#8211; 腾御科技</p>
							</div>
				
			<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1">
				<span class="sr-only">Toggle navigation</span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
			</button>
		</div>

		<!-- Collect the nav links, forms, and other content for toggling -->
		<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
			<ul id="menu-menu" class="nav navbar-nav navbar-right"><li id="menu-item-288" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-288"><a href="index.html">首页<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-34" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-34 dropdown"><a>产品与服务<img class="arrow" src="images/brightarrow.png"/><span class="caret"></span></a>
<ul class="dropdown-menu">
	<li id="menu-item-328" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-328"><a href="building_fire_center.html">楼宇消防接入中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-329" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-329"><a href="government_fire_center.html">政府消防数据中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-331" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-331"><a href="group_fire_center.html">集团消防数据中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-332" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-332"><a href="smart_electrical.html">智慧用电<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-333" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-333"><a href="our_services.html">我们的服务<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-334" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-334"><a target="_blank" rel="noopener" href="https://book.yunzhan365.com/bookcase/ukwe/index.html">产品资料<img class="arrow" src="images/brightarrow.png"/></a></li>
</ul>
</li>
<li id="menu-item-35" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-35"><a href="success_cases.html">成功案例<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-36" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-36"><a href="industry_news.html">行业动态<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-37" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-37"><a href="about_tengyu.html">关于腾御<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-38" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-38"><a href="technical_support.html">技术支持<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-40" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-40"><a href="contact_us.html">联系我们<img class="arrow" src="images/brightarrow.png"/></a></li>
</ul>		
		</div>
	</div>
</nav>	
<!-- End of Navbar -->

<!-- <div style="height: 96px;width: 100%;"></div> -->

<style>
	.arrow {display: none;}
	.container {width: 1200px;padding:0;}
	.maincontainer {width:100%;padding:0 100px;}
	.custom-logo {width: 91px;height: 42px;}
	/* #page {position: relative;}
	.navbar {position: fixed;left:0;right:0;z-index: 999999999;} */
	@media screen and (max-width: 1600px) {
		.maincontainer {width:100%;padding:0 24px;}
	}
	@media screen and (max-width: 1200px) {
		.container {width: 100%;}
	}
	@media screen and (max-width: 1100px) {
		.container {width: auto;}
		.maincontainer {width:100%;padding:0 14px 0 24px;}
		.nav > li {z-index: 999;}
		.navbar-nav>li a{display: flex;align-items: center;justify-content: space-between;}
		.navbar-nav li {border-bottom: 1px solid rgba(0,0,0,.05);}
		.navbar-nav li .arrow {width: 16px;height: 16px; display: inline-block}
		.navbar-nav li.dropdown>a .arrow {display: none;}
		.caret {margin-right: 20px;margin-top: 2px;margin-left:0;border-top:6px dashed;border-right: 6px solid transparent;border-left:6px solid transparent;}
		.navbar-nav .dropdown-menu {display: block;background: #F1F1F1;border:0;box-shadow:none;-webkit-box-shadow:none;}
		.dropdown-menu > li > a {background: #f1f1f1 !important;color: #000;border-bottom:0;padding: 15px;}
		.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {color: #337ab7;}

		.navbar-collapse {position: absolute;right: 0px;left: 0px;background: rgb(255, 255, 255);z-index:9999999;}

		.navbar {min-height: 56px;}
		.navbar-toggle {margin: 8px 15px 8px 0px;}
		.custom-logo {width: 52px;height: 24px;}
		.navbar-default .navbar-toggle {border:0;}

		/* .navbar-toggle {margin: 12px 15px 8px 0px;}
		.logo_imgae {width:96px !important;height: 34px !important;}  */
	}

</style><!-- 文章页面的内容部分 -->
<section>
  <div class="articlepage">
		<div>
			<!--Blog Detail-->
			<div class="sitecontent" style="border: 0;">
				<!-- 文章内容部分 -->
<article class="singlecontent">
		<div class="tip">
		<a href="about_tengyu.html">招贤纳士</a> > 		<a href="">NodeJS软件开发工程师</a>
		</div>
		<header class="entry-header">
			<h3 class="entry-title">NodeJS软件开发工程师</h3>		</header>
		<div class="time"><time datetime="">2020 年 08 月 05 日</time></div>
		<div class="entry-content">

<h4>岗位职责：</h4>
<p>– 基于NodeJS的快速开发平台的设计与实现。<br>
– 物联网业务平台后端逻辑和接口实现。<br>
– 分布式数据存储，海量数据分析，（日增长千万条数据）</p>

<h4>岗位要求：</h4>
<p>– 3年以上NodeJS后端开发经验；<br>
– 有成熟的Koa或者Express项目经验。<br>
– 熟练掌握mongodb，mysql, redis等常用数据库；<br>
– 熟悉JavaScript事件机制，熟悉ES6特性；<br>
– 熟悉MVC开发模式;<br>
– 有分布式项目经验优先。</p>
		</div>
</article>

<style>
		.singlecontent .tip {margin-bottom: 48px;}
		.singlecontent .tip a{font-size: 14px;color: #000;}
		.singlecontent .entry-header .entry-title{font-size: 48px;margin-bottom:21px;color: #2D2E30;}
		.singlecontent .time {margin-bottom: 37px;font-size: 16px;color: #2D2E30;}
		.singlecontent .entry-content p {color: #2D2E30;font-size: 16px;line-height: 40px;padding: 15px 0;}
		.singlecontent .entry-content h1 {color: #2D2E30;font-size: 40px;}
		.singlecontent .entry-content h2 {color: #2D2E30;font-size: 36px;}
		.singlecontent .entry-content h3 {color: #2D2E30;font-size: 32px;}
		.singlecontent .entry-content h4 {color: #2D2E30;font-size: 28px;}
		.singlecontent .entry-content h5 {color: #2D2E30;font-size: 24px;}
		.singlecontent .entry-content h6 {color: #2D2E30;font-size: 20px;}
		@media screen and (max-width: 768px) {
			.singlecontent {width: 100%;padding:0 24px;}
			.singlecontent .tip {margin-bottom: 20px;}
			.singlecontent .tip a{font-size: 12px;color: #000;}
			.singlecontent .entry-header .entry-title{font-size: 24px;margin-bottom:8px;}
			.singlecontent .time {margin-bottom: 24px;font-size: 12px;}
			.singlecontent .entry-content p {font-size: 14px;padding: 12px 0;line-height: 24px;}
			.singlecontent .entry-content h1 {color: #2D2E30;font-size: 32px;}
			.singlecontent .entry-content h2 {color: #2D2E30;font-size: 28px;}
			.singlecontent .entry-content h3 {color: #2D2E30;font-size: 24px;}
			.singlecontent .entry-content h4 {color: #2D2E30;font-size: 20px;}
		}
</style>


							</div>
			<!--/End of Blog Detail-->
		</div>
	</div>
</section>

<style>
	.articlepage {width: 980px;margin:0 auto;}
	.sitecontent {padding-bottom: 80px;padding-top: 45px;}
	@media screen and (max-width: 768px) {
		.articlepage {width: 100%;}
		.sitecontent {padding-bottom: 80px;padding-top: 18px;}
	}
</style>

<!-- Footer Section -->
<footer id="footer">
	<div class="container pcbox">
        <div class="footercont">
            <div class="first">
                <a href="about_tengyu.html">关于我们</a>
                <a href="success_cases.html">成功案例</a>
                <a href="contact_us.html">合作咨询</a>
                <a href="industry_news.html">行业动态</a>
            </div>
            <div class="second">
                <p>腾御（上海）信息科技有限公司</p>
                <p><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a> | <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a></p>
            </div>
            <div class="third">
                <p>Copyright©2019TENGYU.,Ltd 版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
            </div>
        </div>
    </div>

    <div class="container mobbox">
        <div class="footercont">
            <ul>
                <li><a href="about_tengyu.html"><span>关于我们</span> <img src="images/rightarrow.png" alt=""></a></li>
                <li><a href="success_cases.html"><span>成功案例</span> <img src="images/rightarrow.png" alt=""></a></li>
                <li><a href="contact_us.html"><span>合作咨询</span> <img src="images/rightarrow.png" alt=""></a></li>
                <li><a href="industry_news.html"><span>行业动态</span> <img src="images/rightarrow.png" alt=""></a></li>
            </ul>
            <div class="footerbottom">
                <p class="company">腾御（上海）信息科技有限公司</p>
                <p><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a></p>
                <p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a></p>
                <p>Copyright©2019TENGYU.,Ltd</p>
                <p>版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
            </div>
        </div>
    </div>
</footer>
</div>
<!-- /End of Footer Section -->

<!--Scroll To Top-->
<a href="#" class="scrollup"><i class="fa fa-chevron-up"></i></a>
<!--/End of Scroll To Top-->
<script type='text/javascript' src='js/script_6.js' id='comment-reply-js'></script>
<script type='text/javascript' src='js/script_6.js' id='wp-embed-js'></script>

<style>
    #footer {text-align: center;background: #1E3854;}
    .footercont p {color: #fff;}
    .footercont .first {margin-top: 42px;font-size: 16px;}
    .footercont .first a {margin:0 20px;color: #F0F0F0;}
    .footercont .second {margin-top: 35px;font-size: 10px;}
    .footercont .second a{color: #fff;}
    .footercont .third {margin-top: 5px;font-size: 10px;margin-bottom: 40px;}
    .pcbox {display: block;}
    .mobbox {display: none;}
    @media screen and (max-width: 1100px) {
        .container {width: 100%;}
    }
    @media screen and (max-width: 768px) {
        .container {width: 100%;}
        .pcbox {display: none;}
        .mobbox {display: block;}
        .mobbox ul li a{height: 48px;line-height: 48px;border-bottom:1px solid rgba(255,255,255,.1);margin:0;padding:0;display: flex;justify-content: space-between;align-items: center;padding: 0 24px;}
        .mobbox ul li a span{color: #fff;font-size: 12px;}
        .mobbox ul li img {width: 16px;height:16px;}
        .mobbox .footerbottom {padding:0 24px 27px;}
        .mobbox .footerbottom p {color: #fff;font-size: 12px;text-align: left;}
        .mobbox .footerbottom p.company {line-height: 40px;}
        .mobbox .footerbottom p a{color: #fff;font-size: 12px;}
    }
</style>

<script>
    jQuery(document).ready(function($) {

        $('.i-scrollto').each(function () {
            _this = $(this);

            _this.click(function(){

                var target_offset = _this.offset();
                var target_top = target_offset.top;

                $('html, body').animate({scrollTop:target_top}, 600, 'easeInSine');
            });
        });
    });
    // 如果文章页面body的高度没有整个网页的固定高度高，就让footer部分固定在底部
    console.log(document.body.offsetHeight)
    console.log(document.documentElement.clientHeight)
    // setTimeout(() => {
        if (document.body.offsetHeight<document.documentElement.clientHeight) {
            var footersidebar = document.querySelector('#footer');
            footersidebar.style.position = 'fixed';
            footersidebar.style.bottom = 0;
            footersidebar.style.left = 0;
            footersidebar.style.right = 0;
        }
    // }, 500);
</script>

<link rel="stylesheet" id="nx-box-shortcodes-css" href="css/box-shortcodes.css" type="text/css" media="all" />

</body>
</html>
