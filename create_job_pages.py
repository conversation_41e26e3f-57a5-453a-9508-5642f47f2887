#!/usr/bin/env python3
"""
创建招聘职位详情页面
"""

import os

# 职位信息
jobs = [
    {
        "filename": "job_frontend_developer.html",
        "title": "web前端开发工程师",
        "date": "2020 年 08 月 06 日",
        "responsibilities": """– 负责数据中心、管理平台、移动Wap、小程序的前段设计与开发；<br>
– 数据可视化的设计与实现；<br>
– 参与相关页面的Web&Wap前端架构设计、核心代码的编写；<br>
– 进行详细设计、代码开发, 配合测试, 高质量完成项目；<br>
– 按照项目计划，按时提交高质量代码，完成开发任务；""",
        "requirements": """– 熟悉主流前端框架，如vue、react、angular中的1~2种；<br>
– 熟悉RequireJS、webpack，且现有项目中有所实践；<br>
– 有大型项目框架设计实施经验；<br>
– 有全栈发展意愿优先。公司将提供你的学习和提升空间；<br>
– 具有良好的沟通能力和团队合作精神。"""
    },
    {
        "filename": "job_sales_manager.html",
        "title": "销售经理",
        "date": "2020 年 08 月 06 日",
        "responsibilities": """– 负责公司产品的销售及推广；<br>
– 完成公司制定的销售指标，并定期与上海各区物业经理沟通；<br>
– 负责辖区市场信息的收集及竞争对手的分析；<br>
– 维护客户关系，与客户形成长期战略合作；""",
        "requirements": """– 有消防行业从业经验，熟悉消防行业业务流程；-45岁；<br>
– 具有较强的沟通能力及交际能力，具有良好的亲和力；<br>
– 具备一定的市场分析及判断能力，良好的客户服务意识；<br>
– 有责任心，能承受较大的工作压力；<br>
– 有团队协作精神，勇于挑战。"""
    },
    {
        "filename": "job_fire_project_manager.html",
        "title": "消防项目经理",
        "date": "2020 年 08 月 06 日",
        "responsibilities": """– 按照销售部等提供的客户信息及需求，做好消防物联网项目的售前调研，制定解决方案；<br>
– 负责消防物联网施工项目的成本测算，对项目顺利施工、调试、验收、移交负责；<br>
– 负责施工进度、质量、成本、安全等方面的全面把控；<br>
– 对消防物联网项目进行现场维护服务，做好平台使用培训工作，反馈客户使用情况；<br>
– 协助销售部做好业务拓展保障工作、维护期间维护好客户关系，提升客户满意度；<br>
– 配合研发部门对系统应用平台及硬件产品的测试与反馈；<br>
– 协助公司其他部门完成相关工作。""",
        "requirements": """– 全日制大专以上学历，机电、消防等相关专业；<br>
– 有两年以上担任消防项目经理工作经验，至少管理过1个以上完整消防工程施工项目经历；<br>
– 有良好的沟通能力、协调能力及组织能力，良好的现场施工管理能力；<br>
– 熟练使用CAD，看懂消防相关专业图纸；<br>
– 熟悉消防材料、设备性能，掌握消防各系统的工作原理及专业知识；<br>
– 能独立完成现场施工管理、项目工程资料编制、竣工资料整理；<br>
– 具备基本的消防维保操作能力；<br>
– 有消防物联网或智慧安防等相关施工管理经验者优先。"""
    },
    {
        "filename": "job_electrician.html",
        "title": "电工",
        "date": "2020 年 08 月 06 日",
        "responsibilities": """– 负责消防物联网硬件设备的现场安装和调试；<br>
– 负责消防物联网售后维护工作的现场实施；<br>
– 完成项目经理安排的其他相关工作。""",
        "requirements": """– 高中以上学历，年龄45岁以下；<br>
– 3年以上建筑消防水、电系统现场施工经验；<br>
– 能够适应加班；<br>
– 持有水暖工、电工、消防操作员等证书优先录用；<br>
– 会看消防专业图纸的优先录用。"""
    }
]

def create_job_page(job):
    """创建单个职位页面"""
    html_content = f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>	
	<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
	
<title>{job["title"]} &#8211; 九消消防物联网</title>
<meta name='robots' content='max-image-preview:large' />

<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; Feed" href="index.html" />
<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; 评论Feed" href="index.html" />
		<script type="text/javascript">
			window._wpemojiSettings = {{"baseUrl":"https:\\/\\/s.w.org\\/images\\/core\\/emoji\\/13.0.1\\/72x72\\/","ext":".png","svgUrl":"https:\\/\\/s.w.org\\/images\\/core\\/emoji\\/13.0.1\\/svg\\/","svgExt":".svg","source":{{"concatemoji":"\\/wp-includes\\/js\\/wp-emoji-release.min.js?ver=5.7.2"}}}};
		</script>
		<style type="text/css">
img.wp-smiley,
img.emoji {{
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}}
</style>
	<link rel='stylesheet' id='wp-block-library-css'  href='css/style_1.css' type='text/css' media='all' />

<link rel='stylesheet' id='busiprof-style-css'  href='css/style.css' type='text/css' media='all' />
<link rel='stylesheet' id='bootstrap-css-css'  href='css/bootstrap.css' type='text/css' media='all' />
<link rel='stylesheet' id='busiporf-custom-css-css'  href='css/custom.css' type='text/css' media='all' />
<link rel='stylesheet' id='flexslider-css-css'  href='css/flexslider.css' type='text/css' media='all' />

<link rel='stylesheet' id='font-awesome-css-css'  href='css/font-awesome.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='lazyprof-parent-style-css'  href='css/style.css' type='text/css' media='all' />
<script type='text/javascript' src='js/script_1.js' id='jquery-js'></script>
<script type='text/javascript' src='js/script_2.js' id='bootstrap-js-js'></script>
<script type='text/javascript' src='js/script_3.js' id='busiporf-custom-js-js'></script>
<script type='text/javascript' src='https:' id='xiaofang-js'></script>
<link rel="" href="" />
 
<meta name="generator" content="WordPress 5.7.2" />
<link rel="canonical" href="{job["filename"]}" />
<link rel='shortlink' href='{job["filename"]}' />
		<!-- Custom Logo: hide header text -->
		<style id="custom-logo-css" type="text/css">
			.site-title, .site-description {{
				position: absolute;
				clip: rect(1px, 1px, 1px, 1px);
			}}
		</style>
		

<style type="text/css">.recentcomments a{{display:inline !important;padding:0 !important;margin:0 !important;}}</style><link rel="icon" href="images/logo1.png" sizes="32x32" />
<link rel="icon" href="images/logo1.png" sizes="192x192" />
<link rel="apple-touch-icon" href="images/logo1.png" />
<meta name="msapplication-TileImage" content="images/logo1.png" />
	
</head>
<body class="post-template-default single single-post single-format-standard wp-custom-logo">'''
    
    # 写入文件
    with open(f"119net_scraped/{job['filename']}", 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ 已创建 {job['filename']}")

if __name__ == "__main__":
    print("🔧 开始创建招聘职位详情页面...")
    
    for job in jobs:
        create_job_page(job)
    
    print("🎉 所有职位页面创建完成！")
