<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
        }
        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            min-width: 150px;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .refresh-btn {
            display: block;
            margin: 30px auto;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background: #0056b3;
        }
        .timestamp {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 20px;
        }
        .api-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .api-info h3 {
            margin-top: 0;
            color: #495057;
        }
        .api-url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px 10px;
            border-radius: 3px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 119.net 统计数据 API 测试</h1>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="buildNum">-</div>
                <div class="stat-label">保护的建筑数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="positionNum">-</div>
                <div class="stat-label">监测点位数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="riskNum">-</div>
                <div class="stat-label">处理风险事件数</div>
            </div>
        </div>
        
        <button class="refresh-btn" onclick="loadData()">🔄 刷新数据</button>
        
        <div class="timestamp" id="timestamp"></div>
        
        <div class="api-info">
            <h3>API 信息</h3>
            <p><strong>接口地址:</strong> <span class="api-url">/api/statistics</span></p>
            <p><strong>说明:</strong> 这个API返回动态的统计数据，每次请求都会有小幅变化，模拟实时数据更新。</p>
            <p><strong>数据特点:</strong></p>
            <ul>
                <li>建筑数: 基础值 3156，变化范围 ±50~100</li>
                <li>监测点位数: 基础值 2,445,811，变化范围 ±1000~5000</li>
                <li>风险事件数: 基础值 8,517,477，变化范围 ±10000~50000</li>
            </ul>
        </div>
    </div>

    <script>
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
        
        function loadData() {
            fetch('/api/statistics')
                .then(response => response.json())
                .then(data => {
                    console.log('API响应:', data);
                    
                    if (data.code === 0) {
                        document.getElementById('buildNum').textContent = formatNumber(data.data.buildNum);
                        document.getElementById('positionNum').textContent = formatNumber(data.data.positionNum);
                        document.getElementById('riskNum').textContent = formatNumber(data.data.riskNum);
                        
                        const timestamp = new Date(data.timestamp * 1000);
                        document.getElementById('timestamp').textContent = 
                            '最后更新: ' + timestamp.toLocaleString('zh-CN');
                    } else {
                        alert('获取数据失败: ' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    alert('请求失败: ' + error.message);
                });
        }
        
        // 页面加载时自动获取数据
        window.onload = function() {
            loadData();
        };
    </script>
</body>
</html>
