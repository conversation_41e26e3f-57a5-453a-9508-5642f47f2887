<!DOCTYPE html>
<html lang="zh-CN">
<head>	
	<meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
	
		
<title>政府数据中心 &#8211; 九消消防物联网</title>
<meta name='robots' content='max-image-preview:large' />



<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; Feed" href="index.html?feed=rss2" />
<link rel="alternate" type="application/rss+xml" title="九消消防物联网 &raquo; 评论Feed" href="index.html?feed=comments-rss2" />
		<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/13.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.7.2"}};
			!function(e,a,t){var n,r,o,i=a.createElement("canvas"),p=i.getContext&&i.getContext("2d");function s(e,t){var a=String.fromCharCode;p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,e),0,0);e=i.toDataURL();return p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,t),0,0),e===i.toDataURL()}function c(e){var t=a.createElement("script");t.src=e,t.defer=t.type="text/javascript",a.getElementsByTagName("head")[0].appendChild(t)}for(o=Array("flag","emoji"),t.supports={everything:!0,everythingExceptFlag:!0},r=0;r<o.length;r++)t.supports[o[r]]=function(e){if(!p||!p.fillText)return!1;switch(p.textBaseline="top",p.font="600 32px Arial",e){case"flag":return s([127987,65039,8205,9895,65039],[127987,65039,8203,9895,65039])?!1:!s([55356,56826,55356,56819],[55356,56826,8203,55356,56819])&&!s([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]);case"emoji":return!s([55357,56424,8205,55356,57212],[55357,56424,8203,55356,57212])}return!1}(o[r]),t.supports.everything=t.supports.everything&&t.supports[o[r]],"flag"!==o[r]&&(t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&t.supports[o[r]]);t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&!t.supports.flag,t.DOMReady=!1,t.readyCallback=function(){t.DOMReady=!0},t.supports.everything||(n=function(){t.readyCallback()},a.addEventListener?(a.addEventListener("DOMContentLoaded",n,!1),e.addEventListener("load",n,!1)):(e.attachEvent("onload",n),a.attachEvent("onreadystatechange",function(){"complete"===a.readyState&&t.readyCallback()})),(n=t.source||{}).concatemoji?c(n.concatemoji):n.wpemoji&&n.twemoji&&(c(n.twemoji),c(n.wpemoji)))}(window,document,window._wpemojiSettings);
		</script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
	<link rel='stylesheet' id='wp-block-library-css'  href='css/style_1.css' type='text/css' media='all' />

<link rel='stylesheet' id='busiprof-style-css'  href='css/style.css' type='text/css' media='all' />
<link rel='stylesheet' id='bootstrap-css-css'  href='css/bootstrap.css' type='text/css' media='all' />
<link rel='stylesheet' id='busiporf-custom-css-css'  href='css/custom.css' type='text/css' media='all' />
<link rel='stylesheet' id='flexslider-css-css'  href='css/flexslider.css' type='text/css' media='all' />



<link rel='stylesheet' id='font-awesome-css-css'  href='css/font-awesome.min.css' type='text/css' media='all' />
<link rel='stylesheet' id='lazyprof-parent-style-css'  href='css/style.css' type='text/css' media='all' />
<script type='text/javascript' src='js/script_1.js' id='jquery-js'></script>
<script type='text/javascript' src='js/script_2.js' id='bootstrap-js-js'></script>
<script type='text/javascript' src='js/script_3.js' id='busiporf-custom-js-js'></script>
<script type='text/javascript' src='https:' id='xiaofang-js'></script>
<link rel="https://api.w.org/" href="index.htmlindex.php?rest_route=/" /><link rel="alternate" type="application/json" href="index.htmlindex.php?rest_route=/wp/v2/pages/320" />
 
<meta name="generator" content="WordPress 5.7.2" />
<link rel="canonical" href="government_fire_center.html" />
<link rel='shortlink' href='index.html?p=320' />
		<!-- Custom Logo: hide header text -->
		<style id="custom-logo-css" type="text/css">
			.site-title, .site-description {
				position: absolute;
				clip: rect(1px, 1px, 1px, 1px);
			}
		</style>
		<link rel="alternate" type="application/json+oembed" href="index.htmlindex.php?rest_route=%2Foembed%2F1.0%2Fembed&#038;url=https%3A%2F%2Fwww.119.net%2F%3Fpage_id%3D320" />
<link rel="alternate" type="text/xml+oembed" href="index.htmlindex.php?rest_route=%2Foembed%2F1.0%2Fembed&#038;url=https%3A%2F%2Fwww.119.net%2F%3Fpage_id%3D320&#038;format=xml" />
<style type="text/css">.recentcomments a{display:inline !important;padding:0 !important;margin:0 !important;}</style><link rel="icon" href="images/logo1.png" sizes="32x32" />
<link rel="icon" href="images/logo1.png" sizes="192x192" />
<link rel="apple-touch-icon" href="images/logo1.png" />
<meta name="msapplication-TileImage" content="images/logo1.png" />
	
</head>
<body class="page-template page-template-template-govdata page-template-template-govdata-php page page-id-320 wp-custom-logo">
	
<div id="page" class="site">
	<!-- <a class="skip-link busiprof-screen-reader" href="#content">Skip to content</a> -->
<!-- Navbar -->	
<nav class="navbar navbar-default">
	<!-- <div class="container"> -->
	<div class="maincontainer">
		<!-- Brand and toggle get grouped for better mobile display -->
		<div class="navbar-header">
			<span class="navbar-brand"><a href="index.html" class="custom-logo-link" rel="home"><img width="91" height="42" src="images/logo.png" class="custom-logo" alt="九消消防物联网" /></a></span>			<div class="custom-logo-link-url">
	    	<h1 class="site-title"><a class="navbar-brand" href="index.html" >九消消防物联网</a>
	    	</h1>
	    							<p class="site-description">智慧消防 &#8211; 腾御科技</p>
								</div>
				
			<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1">
				<span class="sr-only">Toggle navigation</span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
			</button>
		</div>

		<!-- Collect the nav links, forms, and other content for toggling -->
		<div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
			<ul id="menu-menu" class="nav navbar-nav navbar-right"><li id="menu-item-288" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-288"><a href="index.html">首页<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-34" class="menu-item menu-item-type-custom menu-item-object-custom current-menu-ancestor current-menu-parent menu-item-has-children menu-item-34 dropdown active"><a>产品与服务<img class="arrow" src="images/brightarrow.png"/><span class="caret"></span></a>
<ul class="dropdown-menu">
	<li id="menu-item-328" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-328"><a href="building_fire_center.html">楼宇消防接入中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-329" class="menu-item menu-item-type-post_type menu-item-object-page current-menu-item page_item page-item-320 current_page_item menu-item-329 active"><a href="government_fire_center.html">政府消防数据中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-331" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-331"><a href="group_fire_center.html">集团消防数据中心<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-330" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-330"><a href="smart_electrical.html">智慧用电<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-332" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-332"><a href="our_services.html">我们的服务<img class="arrow" src="images/brightarrow.png"/></a></li>
	<li id="menu-item-738" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-738"><a href="https://book.yunzhan365.com/bookcase/ukwe/index.html">产品资料<img class="arrow" src="images/brightarrow.png"/></a></li>
</ul>
</li>
<li id="menu-item-36" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-36"><a href="success_cases.html">成功案例<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-37" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-37"><a href="industry_news.html">行业动态<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-38" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-38"><a href="about_tengyu.html">关于腾御<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-39" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-39"><a href="technical_support.html">技术支持<img class="arrow" src="images/brightarrow.png"/></a></li>
<li id="menu-item-40" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-40"><a href="contact_us.html">联系我们<img class="arrow" src="images/brightarrow.png"/></a></li>
</ul>			
		</div>
	</div>
</nav>	
<!-- End of Navbar -->
<!-- <div style="height: 96px;width: 100%;"></div> -->

<style>
	.arrow {display: none;}
	.container {width: 1200px;padding:0;}
	.maincontainer {width:100%;padding:0 100px;}
	.custom-logo {width: 91px;height: 42px;}
	/* #page {position: relative;}
	.navbar {position: fixed;left:0;right:0;z-index: 999999999;} */
	@media screen and (max-width: 1600px) {
		.maincontainer {width:100%;padding:0 24px;}
	}
	@media screen and (max-width: 1200px) {
		.container {width: 100%;}
	}
	@media screen and (max-width: 1100px) {
		.container {width: auto;}
		.maincontainer {width:100%;padding:0 14px 0 24px;}
		.nav > li {z-index: 999;}
		.navbar-nav>li a{display: flex;align-items: center;justify-content: space-between;}
		.navbar-nav li {border-bottom: 1px solid rgba(0,0,0,.05);}
		.navbar-nav li .arrow {width: 16px;height: 16px; display: inline-block}
		.navbar-nav li.dropdown>a .arrow {display: none;}
		.caret {margin-right: 20px;margin-top: 2px;margin-left:0;border-top:6px dashed;border-right: 6px solid transparent;border-left:6px solid transparent;}
		.navbar-nav .dropdown-menu {display: block;background: #F1F1F1;border:0;box-shadow:none;-webkit-box-shadow:none;} 
		.dropdown-menu > li > a {background: #f1f1f1 !important;color: #000;border-bottom:0;padding: 15px;}
		.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {color: #337ab7;}

		.navbar-collapse {position: absolute;right: 0px;left: 0px;background: rgb(255, 255, 255);z-index:9999999;}

		.navbar {min-height: 56px;}
		.navbar-toggle {margin: 8px 15px 8px 0px;}
		.custom-logo {width: 52px;height: 24px;}
		.navbar-default .navbar-toggle {border:0;}

		/* .navbar-toggle {margin: 12px 15px 8px 0px;}
		.logo_imgae {width:96px !important;height: 34px !important;}  */
	}
	
</style>
<section class="govdata">
  <div class="fan_banner">
    <div class="bannercontent">
      <p class="titletxt">政府消防数据中心</p>
      <p class="p2">消防主管部门用于督查监管</p>
    </div>
  </div>
  <div class="login">
      <img class="datasimg" src="/wp-content/themes/busiprof/images/datas1.jpg"/>
      <div class="left">
          <p class="titletxt">平台介绍</p>
          <p>涵盖消防应急管理全部工作流程和管辖对象。 全国首套消防应急管理整体解决方案。 可以根据管理部门实际需求分拆、组合、定制各子系统，实现点菜式定制消防应急管理接入中心。 有法律依据，有数据标准，有执行细节的政府管理台。 通过消防物联网远程传输设备，将消防设备运行数据动态实时上传到“政府数据中心”建筑消防隐患一目了然。</p>
      </div>
  </div>
  <div class="accesscenter">
    <ul>
      <li>
        <div class="blueline"></div>
        <p>辖区内消防基础数据收集。 九个管理对象：建筑、单位、消防水源、维保、街道等</p>
      </li>
      <li>
        <div class="blueline"></div>
        <p>辖区内消防运维自动督查 火警核实、设备巡查维修监测等</p>
      </li>
      <li>
        <div class="blueline"></div>
        <p>政府需要的数据的输出现场执法、火场作战支持</p>
      </li>
      <li>
        <div class="blueline"></div>
        <p>明确的物联监测标准 支持21套消防系统，278中硬件物联监测，8项业务流程监管，全部有法律依据和数据标准</p>
      </li>
      <li>
        <div class="blueline"></div>
        <p>针对重点单位、九小场所、老旧小区、市政消火栓等场景都有不同的解决方案。</p>
      </li>
    </ul>
  </div>
</section>

<style>
  .govdata {background: #fff;overflow:hidden;}
  .fan_banner {background: url(/wp-content/themes/busiprof/images/productbg.jpg) no-repeat center;background-size: cover;width: 100%;height: 581px;overflow: hidden;}
  .fan_banner .bannercontent {text-align: center;margin-top: 207px;}
  .fan_banner .titletxt {font-size: 72px;color: #fff;letter-spacing: 18px;}
  .fan_banner .p2 {font-size: 24px;color: #fff;margin-top: 48px;}

  .login {width: 1200px;margin:0 auto;background: #fff;display: flex;justify-content: center;flex-direction:row-reverse;}
  .login .left {margin-top: 182px;width: 422px;margin-right: 59px;}
  .login .left .titletxt{font-weight: 700;color: #000;font-size: 48px;margin-bottom: 36px;line-height: 71px;}
  .login .left .titletxt span{color:#0084C9;}
  .login .left p{color: #2D2E30;line-height: 30px;font-size:18px;}
  .login .left .loginbtn {margin-top: 130px;width: 240px;height:68px;text-align: center;line-height: 68px;background: #0085ca;}
  .login .left .loginbtn a{color: #fff;font-size: 24px;}
  .login .datasimg {width: 806px;height: 564px;margin-top: 82px;}

  .accesscenter {background: #fff;padding-bottom: 20px;}
  .accesscenter ul {width: 1200px;margin:0 auto;display: flex;flex-wrap: wrap;margin-top: 34px;}
  .accesscenter ul li{width: 347px;margin: 0 24px 80px;}
  .accesscenter ul li .blueline {width: 104px;height:6px;background: #0469AB;border-radius: 6px;margin:0 auto 39px;}
  .accesscenter ul li p {color: #000;font-size: 18px;line-height: 30px;text-align: center;}
  @media screen and (max-width: 1200px) {
    .login {width: 100%;}
    .login .datasimg {width: 700px;height: auto;}
    .login .left {margin-left: 0;}
    .accesscenter ul {width: 100%;}
    .accesscenter ul li {width: 28%;} 
  }
  @media screen and (max-width: 1100px) {
    .fan_banner {width: 100%;height: 502px;}
    .fan_banner .bannercontent {margin-top: 156px;}
    .fan_banner .titletxt {width: 270px;margin:0 auto;font-size: 50px;letter-spacing: 6px;}
    .fan_banner .p2 {font-size: 14px;margin-top: 16px;}

    .login {width: 100%;display: block;padding-top:16px;padding-bottom: 32px;padding-right: 24px;padding-left: 24px;}
    .login .left {width: 100%;margin:0;}
    .login .left .titletxt{font-size: 32px;margin-bottom: 24px;margin-top: 75px;}
    .login .left p{font-size: 14px;line-height: 24px;}
    .login .left .loginbtn {margin-top: 48px;width: 177px;height:32px;line-height: 32px;}
    .login .left .loginbtn a{font-size: 16px;}
    .login .datasimg {width: 100%;height: 100%;margin-left: 0;margin-top: 36px;}

    .accesscenter {padding-bottom: 90px;}
    .accesscenter ul{width: 100%;display: block;}
    .accesscenter ul li {width: 100%;padding:0 24px;margin:0 auto;margin-top: 48px;}
    .accesscenter ul li .blueline {width: 104px;height:6px;background: #0469AB;border-radius: 6px;margin:0 auto 18px;}
    .accesscenter ul li p {color: #000;font-size: 14px;line-height: 24px;text-align: center;}

  }

</style>

<footer id="footer">
	<div class="container pcbox">		
        <div class="footercont">
            <p class="first"><a href="about_tengyu.html">关于我们</a><a href="success_cases.html">成功案例</a><a href="contact_us.html">合作咨询</a><a href="industry_news.html">行业动态</a></p>
            <p class="second">腾御（上海）信息科技有限公司 | 
              <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a> | 
              <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a>
            </p>
            <p class="third">Copyright©2019TENGYU.,Ltd | 版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
        </div>
	</div>
	<div class="mobbox">	
        <ul>
            <li>
                <a href="about_tengyu.html">
                    <span>关于我们</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="success_cases.html">
                    <span>成功案例</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="contact_us.html">
                    <span>合作咨询</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
            <li>
                <a href="industry_news.html">
                    <span>行业动态</span>
                    <img src="images/rightarrow.png" alt="">
                </a>
            </li>
        </ul>
        <div class="footerbottom">
            <p class="company">腾御（上海）信息科技有限公司</p>
            <p><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">沪ICP备2023030929号</a></p>
            <p><a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011002006339" target="_blank">沪公网安备31011002006339号</a></p>
            <p>Copyright©2019TENGYU.,Ltd</p>
            <p>版权所有：腾御（上海）信息科技有限公司,保留所有权利</p>
        </div>
	</div>
</footer>
</div>
<!-- /End of Footer Section -->

<!--Scroll To Top--> 
<a href="#" class="scrollup"><i class="fa fa-chevron-up"></i></a>
<!--/End of Scroll To Top--> 	
<script type='text/javascript' src='js/script_6.js' id='comment-reply-js'></script>
<script type='text/javascript' src='js/script_6.js' id='wp-embed-js'></script>

<style>
    #footer {text-align: center;background: #1E3854;}
    .footercont p {color: #fff;}
    .footercont .first {margin-top: 42px;font-size: 16px;}
    .footercont .first a {margin:0 20px;color: #F0F0F0;}
    .footercont .second {margin-top: 35px;font-size: 10px;}
    .footercont .second a{color: #fff;}
    .footercont .third {margin-top: 5px;font-size: 10px;margin-bottom: 40px;}
    .pcbox {display: block;}
    .mobbox {display: none;}
    @media screen and (max-width: 1100px) {
        .container {width: 100%;}
    }
    @media screen and (max-width: 768px) {
        .container {width: 100%;}
        .pcbox {display: none;}
        .mobbox {display: block;}
        .mobbox ul li a{height: 48px;line-height: 48px;border-bottom:1px solid rgba(255,255,255,.1);margin:0;padding:0;display: flex;justify-content: space-between;align-items: center;padding: 0 24px;}
        .mobbox ul li a span{color: #fff;font-size: 12px;}
        .mobbox ul li img {width: 16px;height:16px;}
        .mobbox .footerbottom {padding:0 24px 27px;}
        .mobbox .footerbottom p {color: #fff;font-size: 12px;text-align: left;}
        .mobbox .footerbottom p.company {line-height: 40px;}
        .mobbox .footerbottom p a{color: #fff;font-size: 12px;}
    }
</style>

<script>
    jQuery(document).ready(function($) {

        $('.i-scrollto').each(function () {
            _this = $(this);

            _this.click(function(){

                var target_offset = _this.offset();
                var target_top = target_offset.top;

                $('html, body').animate({scrollTop:target_top}, 600, 'easeInSine');
            });
        });
    });
    // 如果文章页面body的高度没有整个网页的固定高度高，就让footer部分固定在底部
    console.log(document.body.offsetHeight)
    console.log(document.documentElement.clientHeight)
    // setTimeout(() => {
        if (document.body.offsetHeight<document.documentElement.clientHeight) {
            var footersidebar = document.querySelector('#footer');
            footersidebar.style.position = 'fixed';
            footersidebar.style.bottom = 0;
            footersidebar.style.left = 0;
            footersidebar.style.right = 0;
        }
    // }, 500);
</script>

<link rel="stylesheet" id="nx-box-shortcodes-css" href="css/box-shortcodes.css" type="text/css" media="all" />

</body>
</html>





