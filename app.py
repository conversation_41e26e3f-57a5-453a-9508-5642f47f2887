#!/usr/bin/env python3
"""
119.net 静态网站服务器
使用Flask提供静态文件服务，支持多页面导航
"""

from flask import Flask, render_template_string, send_from_directory, redirect, url_for
import os
from pathlib import Path

app = Flask(__name__)

# 配置静态文件目录
STATIC_DIR = "119net_scraped"
app.static_folder = STATIC_DIR

@app.route('/')
def index():
    """主页"""
    return send_from_directory(STATIC_DIR, 'index.html')

@app.route('/about')
def about():
    """关于腾御"""
    return send_from_directory(STATIC_DIR, 'about_tengyu.html')

@app.route('/services')
def services():
    """我们的服务"""
    return send_from_directory(STATIC_DIR, 'our_services.html')

@app.route('/building-fire')
def building_fire():
    """楼宇消防接入中心"""
    return send_from_directory(STATIC_DIR, 'building_fire_center.html')

@app.route('/government-fire')
def government_fire():
    """政府消防数据中心"""
    return send_from_directory(STATIC_DIR, 'government_fire_center.html')

@app.route('/group-fire')
def group_fire():
    """集团消防数据中心"""
    return send_from_directory(STATIC_DIR, 'group_fire_center.html')

@app.route('/smart-electrical')
def smart_electrical():
    """智慧用电"""
    return send_from_directory(STATIC_DIR, 'smart_electrical.html')

@app.route('/success-cases')
def success_cases():
    """成功案例"""
    return send_from_directory(STATIC_DIR, 'success_cases.html')

@app.route('/industry-news')
def industry_news():
    """行业动态"""
    return send_from_directory(STATIC_DIR, 'industry_news.html')

@app.route('/technical-support')
def technical_support():
    """技术支持"""
    return send_from_directory(STATIC_DIR, 'technical_support.html')

@app.route('/contact')
def contact():
    """联系我们"""
    return send_from_directory(STATIC_DIR, 'contact_us.html')

@app.route('/fire-hazard-rules')
def fire_hazard_rules():
    """火灾隐患判定规则"""
    return send_from_directory(STATIC_DIR, 'fire_hazard_rules.html')

@app.route('/fire-statistics-rules')
def fire_statistics_rules():
    """火灾统计规则"""
    return send_from_directory(STATIC_DIR, 'fire_statistics_rules.html')

@app.route('/national-standard')
def national_standard():
    """国家标准GB4717"""
    return send_from_directory(STATIC_DIR, 'national_standard_gb4717.html')

# 静态资源路由
@app.route('/css/<path:filename>')
def css_files(filename):
    """CSS文件"""
    return send_from_directory(os.path.join(STATIC_DIR, 'css'), filename)

@app.route('/js/<path:filename>')
def js_files(filename):
    """JavaScript文件"""
    return send_from_directory(os.path.join(STATIC_DIR, 'js'), filename)

@app.route('/images/<path:filename>')
def image_files(filename):
    """图片文件"""
    return send_from_directory(os.path.join(STATIC_DIR, 'images'), filename)

@app.route('/fonts/<path:filename>')
def font_files(filename):
    """字体文件"""
    return send_from_directory(os.path.join(STATIC_DIR, 'fonts'), filename)

@app.route('/static/<path:filename>')
def static_files(filename):
    """其他静态文件"""
    return send_from_directory(os.path.join(STATIC_DIR, 'static'), filename)

@app.route('/data/<path:filename>')
def data_files(filename):
    """数据文件"""
    return send_from_directory(os.path.join(STATIC_DIR, 'data'), filename)

# API路由示例
@app.route('/api/pages')
def api_pages():
    """获取所有页面列表的API"""
    pages = [
        {'name': '首页', 'url': '/', 'file': 'index.html'},
        {'name': '关于腾御', 'url': '/about', 'file': 'about_tengyu.html'},
        {'name': '我们的服务', 'url': '/services', 'file': 'our_services.html'},
        {'name': '楼宇消防接入中心', 'url': '/building-fire', 'file': 'building_fire_center.html'},
        {'name': '政府消防数据中心', 'url': '/government-fire', 'file': 'government_fire_center.html'},
        {'name': '集团消防数据中心', 'url': '/group-fire', 'file': 'group_fire_center.html'},
        {'name': '智慧用电', 'url': '/smart-electrical', 'file': 'smart_electrical.html'},
        {'name': '成功案例', 'url': '/success-cases', 'file': 'success_cases.html'},
        {'name': '行业动态', 'url': '/industry-news', 'file': 'industry_news.html'},
        {'name': '技术支持', 'url': '/technical-support', 'file': 'technical_support.html'},
        {'name': '联系我们', 'url': '/contact', 'file': 'contact_us.html'},
    ]
    return {'pages': pages}

@app.route('/admin')
def admin():
    """管理页面"""
    admin_html = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>119.net 管理面板</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
            .page-list { list-style: none; padding: 0; }
            .page-list li { margin: 10px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007cba; }
            .page-list a { text-decoration: none; color: #007cba; font-weight: bold; }
            .page-list a:hover { color: #005a8b; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
            .stat-card { background: #007cba; color: white; padding: 20px; border-radius: 5px; text-align: center; }
            .stat-number { font-size: 2em; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔥 119.net 网站管理面板</h1>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">13</div>
                    <div>页面总数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">运行中</div>
                    <div>服务状态</div>
                </div>
            </div>

            <h2>📄 页面列表</h2>
            <ul class="page-list">
                <li><a href="/">🏠 首页</a> - index.html</li>
                <li><a href="/about">ℹ️ 关于腾御</a> - about_tengyu.html</li>
                <li><a href="/services">🛠️ 我们的服务</a> - our_services.html</li>
                <li><a href="/building-fire">🏢 楼宇消防接入中心</a> - building_fire_center.html</li>
                <li><a href="/government-fire">🏛️ 政府消防数据中心</a> - government_fire_center.html</li>
                <li><a href="/group-fire">🏭 集团消防数据中心</a> - group_fire_center.html</li>
                <li><a href="/smart-electrical">⚡ 智慧用电</a> - smart_electrical.html</li>
                <li><a href="/success-cases">🏆 成功案例</a> - success_cases.html</li>
                <li><a href="/industry-news">📰 行业动态</a> - industry_news.html</li>
                <li><a href="/technical-support">🔧 技术支持</a> - technical_support.html</li>
                <li><a href="/contact">📞 联系我们</a> - contact_us.html</li>
                <li><a href="/fire-hazard-rules">📋 火灾隐患判定规则</a> - fire_hazard_rules.html</li>
                <li><a href="/fire-statistics-rules">📊 火灾统计规则</a> - fire_statistics_rules.html</li>
                <li><a href="/national-standard">📜 国家标准GB4717</a> - national_standard_gb4717.html</li>
            </ul>

            <h2>🔗 API接口</h2>
            <ul class="page-list">
                <li><a href="/api/pages">📋 获取页面列表</a> - JSON格式</li>
            </ul>
        </div>
    </body>
    </html>
    """
    return render_template_string(admin_html)

if __name__ == '__main__':
    # 检查静态文件目录是否存在
    if not os.path.exists(STATIC_DIR):
        print(f"错误: 静态文件目录 '{STATIC_DIR}' 不存在")
        print("请确保已经运行了网站抓取脚本")
        exit(1)
    
    print("🔥 119.net 网站服务器启动中...")
    print(f"📁 静态文件目录: {STATIC_DIR}")
    print("🌐 访问地址:")
    print("   主页: http://localhost:5000")
    print("   管理面板: http://localhost:5000/admin")
    print("   API: http://localhost:5000/api/pages")
    print("\n按 Ctrl+C 停止服务器")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
